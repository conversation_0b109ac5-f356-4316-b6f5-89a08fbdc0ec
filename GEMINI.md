# Project Overview for AI Models (GEMINI.md)

This document provides essential context and guidelines for any AI model interacting with the `ng-neuratalk` monorepo. Please read this thoroughly before attempting any code modifications or analysis.

## 1. Project Structure & Monorepo Setup

This is a Node.js/TypeScript monorepo managed by npm workspaces.
Key services and packages:

- `bot-builder-service`: Administrative API for managing bots, flows, and knowledge base (FAQs, Intents, Entities).
- `bot-interaction-service`: Handles real-time bot interactions.
- `chat-gateway`: WebSocket gateway for chat.
- `chat-service`: Core chat message processing.
- `studio`: Frontend application (various sub-modules).
- `rasa-server`: Python-based Rasa NLU server.
- `packages/bot-store`: Sequelize models and database connection for shared data.
- `packages/common`: Common utilities, helpers, and shared types.
- `client-hooks`: Frontend hooks.
- `eslint-local-rules`: Custom ESLint rules.

**Important Monorepo Rule:**

- **Always run `npm install` from the project root (`/Users/<USER>/Documents/Sourcefuse/comviva-ngage/ng-neuratalk/`)** to ensure all inter-package dependencies are correctly resolved. Do NOT run `npm install` inside individual service/package directories unless specifically instructed for debugging.

## 2. Key Technologies & Stack

- **Backend:** Node.js, Express.js, TypeScript
- **ORM:** Sequelize (v6.x)
- **Database:** MySQL
- **Validation:** Zod
- **API Documentation:** Swagger/OpenAPI 3.0 (generated via `swagger-jsdoc`)
- **Testing:** Jest
- **Linting/Formatting:** ESLint, Prettier
- **AI/NLU:** Rasa (integrated via `rasa-server` and `bot-builder-service`)

## 3. Core Development Practices & Conventions

- **Code Style:** Adhere strictly to existing ESLint (`.eslintrc.cjs`) and Prettier (`.prettierrc.js`) configurations.
- **TypeScript:** Prioritize strong typing. Avoid `any` unless absolutely necessary and justified.
- **API Design:** Follow RESTful principles.
- **Error Handling:** Use standardized `successResponse` and `errorResponse` helpers from `@neuratalk/common`.
- **Database Interactions:** Use Sequelize models from `@neuratalk/bot-store`. All database operations should ideally be wrapped in transactions for atomicity, especially for complex multi-step operations.
- **Audit Fields:** Fields like `createdAt`, `updatedAt`, `deletedAt`, `createdBy`, `updatedBy`, `deletedBy` are **managed internally by the API/database** and **must NOT be exposed in request payloads (create or update)**. They are automatically handled by Sequelize hooks.
- **Foreign Key IDs in Updates:** Foreign key IDs (e.g., `botId`, `categoryId`, `faqId`, `langId`, `intentId`, `utteranceId`) that define relationships **should NOT be updatable** via PUT/PATCH requests. They are typically set during creation and are immutable for existing records. Schemas reflect this using `.omit()` on update schemas.
- **Strict Schemas:** All Zod schemas are defined with `.strict()` to disallow undeclared properties in payloads.

## 4. Build & Run Commands

- **Clean & Install (Root):**
  ```bash
  rm -rf node_modules package-lock.json # Only if absolutely necessary for deep clean
  npm install # ALWAYS run from project root after cloning or major dependency changes
  ```
- **Build Specific Package:**
  ```bash
  npm run build --workspace=packages/bot-store # Build bot-store first if its types changed
  npm run build --workspace=bot-builder-service # Build other services
  # Or, if in the package directory:
  npm run build # (e.g., in bot-builder-service/)
  ```
- **Run Development Server (Bot Builder Service):**
  ```bash
  npm run dev --workspace=bot-builder-service # Or from bot-builder-service/ directory: npm run dev
  ```
- **Swagger UI:** Access at `http://localhost:3000/api-docs` (after `bot-builder-service` is running).

## 6. General Guidelines for AI Interaction

- **Prioritize Consistency:** Always mimic existing code style, structure, and architectural patterns.
- **Verify Dependencies:** Never assume a library or framework is available. Check `package.json` or existing imports.
- **Read Extensively:** Use `read_file`, `search_file_content`, and `glob` to understand the codebase context before making changes.
- **Explain Critical Commands:** For any `run_shell_command` that modifies the file system or system state, provide a brief explanation of its purpose and potential impact.
- **Self-Correction:** If a change causes errors, analyze the error messages carefully and iterate on the solution.

---

**Last Updated:** 2025-07-20
**Generated by:** Gemini CLI Agent

---
