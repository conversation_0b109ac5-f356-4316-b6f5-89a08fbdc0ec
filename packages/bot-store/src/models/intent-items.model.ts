import {
  DataTypes,
  Model,
  Optional,
  Sequelize,
  BelongsTo,
  BelongsToGetAssociationMixin,
} from "sequelize";
import { BotModel } from "./bot.model";
import { FlowModel } from "./flow.model";

export interface IntentItemsAttributes {
  id: string;
  botId: string;
  flowId?: string | null;
  name: string;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  // Associations
  bot?: BotModel;
  flow?: FlowModel;
}

type IntentItemsCreationAttributes = Optional<
  IntentItemsAttributes,
  "id" | "createdAt" | "updatedAt" | "deletedAt" | "deletedBy" | "flowId" | "createdBy" | "updatedBy"
>;

export class IntentItemsModel
  extends Model<IntentItemsAttributes, IntentItemsCreationAttributes>
  implements IntentItemsAttributes
{
  public id!: string;
  public botId!: string;
  public flowId?: string;
  public name!: string;
  public description?: string;
  public createdAt!: Date;
  public updatedAt!: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public updatedBy!: string;
  public deletedBy?: string;

  // Mixins for associations
  public getBot!: BelongsToGetAssociationMixin<BotModel>;
  public getFlow!: BelongsToGetAssociationMixin<FlowModel>;

  // Properties for eager loading
  public bot?: BotModel;
  public flow?: FlowModel;

  public static associations: {
    bot: BelongsTo;
    flow: BelongsTo;
  };
}

export function initIntentItemsModel(sequelize: Sequelize): typeof IntentItemsModel {
  IntentItemsModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "bots",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      flowId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "flows",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "SET NULL",
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "IntentItemsModel",
      tableName: "intent_items",
      paranoid: true,
      timestamps: true,
      indexes: [
        {
          unique: true,
          fields: ["botId", "name"],
        },
      ],
    },
  );
  return IntentItemsModel;
}
