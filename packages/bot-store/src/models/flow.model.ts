import {
  DataTypes,
  Model,
  Sequelize,
  BelongsTo,
  BelongsToGetAssociationMixin,
  Optional,
} from "sequelize";
import { BotModel } from "./bot.model";

export interface FlowAttributes {
  id: string;
  botId: string;
  name: string;
  description?: string;
  type?: string;
  appId?: string;
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  // Associations
  bot?: BotModel;
}

type FlowCreationAttributes = Optional<
  FlowAttributes,
  "id" | "createdAt" | "updatedAt" | "deletedAt" | "deletedBy" | "createdBy" | "updatedBy"
>;

export class FlowModel
  extends Model<FlowAttributes, FlowCreationAttributes>
  implements FlowAttributes
{
  public id!: string;
  public botId!: string;
  public name!: string;
  public description?: string;
  public type?: string;
  public appId?: string;
  public metadata?: Record<string, any>;
  public readonly createdAt?: Date;
  public readonly updatedAt?: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public updatedBy!: string;
  public deletedBy?: string;

  // Mixins for associations
  public getBot!: BelongsToGetAssociationMixin<BotModel>;

  // Properties for eager loading
  public bot?: BotModel;

  public static associations: {
    bot: BelongsTo;
  };
}

export function initFlowModel(sequelize: Sequelize): typeof FlowModel {
  FlowModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      type: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: "Default",
      },
      appId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
      },
      updatedAt: {
        type: DataTypes.DATE,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
    },
    {
      sequelize,
      tableName: "flows",
      timestamps: true,
      paranoid: true,
    },
  );

  return FlowModel;
}
