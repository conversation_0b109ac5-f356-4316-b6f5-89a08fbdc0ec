import { Sequelize } from "sequelize";
import { BotModel, initBotModel } from "./bot.model";
import { FlowModel, initFlowModel } from "./flow.model";
import { BotModelModel, initBotModelModel } from "./bot-model.model";
import { FaqItemsModel, initFaqItemsModel } from "./faq-items.model";
import { IntentItemsModel, initIntentItemsModel } from "./intent-items.model";
import { EntitiesModel, initEntitiesModel } from "./entities.model";
import { LanguageModel, initLanguageModel } from "./language.model";
import { BotLanguageModel, initBotLanguageModel } from "./bot-language.model";
import { FaqCategoryModel, initFaqCategoryModel } from "./faq-category.model";
import { FaqTranslationModel, initFaqTranslationModel } from "./faq-translation.model";
import { IntentUtteranceModel, initIntentUtteranceModel } from "./intent-utterance.model";
import {
  UtteranceTranslationModel,
  initUtteranceTranslationModel,
} from "./utterance-translation.model";

export interface Models {
  Bot: typeof BotModel;
  Flow: typeof FlowModel;
  BotModel: typeof BotModelModel;
  FaqItems: typeof FaqItemsModel;
  IntentItems: typeof IntentItemsModel;
  Entities: typeof EntitiesModel;
  Language: typeof LanguageModel;
  BotLanguage: typeof BotLanguageModel;
  FaqCategory: typeof FaqCategoryModel;
  FaqTranslation: typeof FaqTranslationModel;
  IntentUtterance: typeof IntentUtteranceModel;
  UtteranceTranslation: typeof UtteranceTranslationModel;
}

export function initModels(sequelize: Sequelize): Models {
  const Bot = initBotModel(sequelize);
  const Flow = initFlowModel(sequelize);
  const BotModel = initBotModelModel(sequelize);
  const FaqItems = initFaqItemsModel(sequelize);
  const IntentItems = initIntentItemsModel(sequelize);
  const Entities = initEntitiesModel(sequelize);
  const Language = initLanguageModel(sequelize);
  const BotLanguage = initBotLanguageModel(sequelize);
  const FaqCategory = initFaqCategoryModel(sequelize);
  const FaqTranslation = initFaqTranslationModel(sequelize);
  const IntentUtterance = initIntentUtteranceModel(sequelize);
  const UtteranceTranslation = initUtteranceTranslationModel(sequelize);

  // Define associations
  Bot.hasMany(Flow, { foreignKey: "botId", as: "flows" });
  Flow.belongsTo(Bot, { foreignKey: "botId", as: "bot" });

  Bot.hasMany(BotModel, { foreignKey: "botId", as: "models" });
  BotModel.belongsTo(Bot, { foreignKey: "botId", as: "bot" });

  Bot.belongsTo(BotModel, { foreignKey: "previewModelId", as: "previewModel" });
  Bot.belongsTo(BotModel, {
    foreignKey: "publishedModelId",
    as: "publishedModel",
  });

  // Bot-Language associations
  Bot.hasMany(BotLanguage, { foreignKey: "botId", as: "botLanguages" });
  BotLanguage.belongsTo(Bot, { foreignKey: "botId", as: "bot" });
  Language.hasMany(BotLanguage, { foreignKey: "langId", as: "botLanguages" });
  BotLanguage.belongsTo(Language, { foreignKey: "langId", as: "language" });

  // FAQ associations
  Bot.hasMany(FaqCategory, { foreignKey: "botId", as: "faqCategories" });
  FaqCategory.belongsTo(Bot, { foreignKey: "botId", as: "bot" });

  Bot.hasMany(FaqItems, { foreignKey: "botId", as: "faqItems" });
  FaqItems.belongsTo(Bot, { foreignKey: "botId", as: "bot" });
  Flow.hasMany(FaqItems, { foreignKey: "flowId", as: "faqItems" });
  FaqItems.belongsTo(Flow, { foreignKey: "flowId", as: "flow" });
  FaqCategory.hasMany(FaqItems, { foreignKey: "categoryId", as: "faqItems" });
  FaqItems.belongsTo(FaqCategory, { foreignKey: "categoryId", as: "category" });

  FaqItems.hasMany(FaqTranslation, { foreignKey: "faqId", as: "faqTranslations" });
  FaqTranslation.belongsTo(FaqItems, { foreignKey: "faqId", as: "faqItem" });
  Language.hasMany(FaqTranslation, { foreignKey: "langId", as: "faqTranslations" });
  FaqTranslation.belongsTo(Language, { foreignKey: "langId", as: "language" });

  // Intent associations
  Bot.hasMany(IntentItems, { foreignKey: "botId", as: "intentItems" });
  IntentItems.belongsTo(Bot, { foreignKey: "botId", as: "bot" });
  Flow.hasMany(IntentItems, { foreignKey: "flowId", as: "intentItems" });
  IntentItems.belongsTo(Flow, { foreignKey: "flowId", as: "flow" });

  IntentItems.hasMany(IntentUtterance, { foreignKey: "intentId", as: "intentUtterances" });
  IntentUtterance.belongsTo(IntentItems, { foreignKey: "intentId", as: "intentItem" });

  IntentUtterance.hasMany(UtteranceTranslation, {
    foreignKey: "utteranceId",
    as: "utteranceTranslations",
  });
  UtteranceTranslation.belongsTo(IntentUtterance, {
    foreignKey: "utteranceId",
    as: "utterance",
  });
  Language.hasMany(UtteranceTranslation, {
    foreignKey: "langId",
    as: "utteranceTranslations",
  });
  UtteranceTranslation.belongsTo(Language, { foreignKey: "langId", as: "language" });

  // Entity associations
  Bot.hasMany(Entities, { foreignKey: "botId", as: "entities" });
  Entities.belongsTo(Bot, { foreignKey: "botId", as: "bot" });
  IntentItems.hasMany(Entities, { foreignKey: "intentId", as: "entities" });
  Entities.belongsTo(IntentItems, { foreignKey: "intentId", as: "intentItem" });

  return {
    Bot,
    Flow,
    BotModel,
    FaqItems,
    IntentItems,
    Entities,
    Language,
    BotLanguage,
    FaqCategory,
    FaqTranslation,
    IntentUtterance,
    UtteranceTranslation,
  };
}

export * from "./bot.model";
export * from "./flow.model";
export * from "./bot-model.model";
export * from "./faq-items.model";
export * from "./intent-items.model";
export * from "./entities.model";
export * from "./language.model";
export * from "./bot-language.model";
export * from "./faq-category.model";
export * from "./faq-translation.model";
export * from "./intent-utterance.model";
export * from "./utterance-translation.model";
