import {
  DataTypes,
  <PERSON>,
  <PERSON>quelize,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  HasManyGetAssociationsMixin,
  HasManyAddAssociationMixin,
  HasManyCreateAssociationMixin,
  BelongsToGetAssociationMixin,
  Optional,
} from "sequelize";
import { FlowModel } from "./flow.model";
import { BotModelModel } from "./bot-model.model";

export enum BotStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export interface BotAttributes {
  id: string;
  name: string;
  description?: string;
  status: BotStatus;
  domain?: string;
  settings?: Record<string, any>;
  metadata?: Record<string, any>;
  createdBy: string;
  updatedBy: string;
  previewModelId?: string;
  publishedModelId?: string;
  createdAt: Date;
  updatedAt: Date;

  // Associations
  flows?: FlowModel[];
  models?: BotModelModel[];
  previewModel?: BotModelModel;
  publishedModel?: BotModelModel;
}

type BotCreationAttributes = Optional<BotAttributes, "id" | "createdAt" | "updatedAt">;

export class BotModel extends Model<BotAttributes, BotCreationAttributes> implements BotAttributes {
  public id!: string;
  public name!: string;
  public description?: string;
  public status!: BotStatus;
  public settings?: BotSettings;
  public metadata?: Record<string, any>;
  public createdBy!: string;
  public updatedBy!: string;
  public previewModelId?: string;
  public publishedModelId?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Mixins for associations
  public getFlows!: HasManyGetAssociationsMixin<FlowModel>;
  public addFlow!: HasManyAddAssociationMixin<FlowModel, string>;
  public createFlow!: HasManyCreateAssociationMixin<FlowModel>;

  public getModels!: HasManyGetAssociationsMixin<BotModelModel>;
  public addModel!: HasManyAddAssociationMixin<BotModelModel, string>;
  public createModel!: HasManyCreateAssociationMixin<BotModelModel>;

  public getPreviewModel!: BelongsToGetAssociationMixin<BotModelModel>;
  public getPublishedModel!: BelongsToGetAssociationMixin<BotModelModel>;

  // Properties for eager loading
  public flows?: FlowModel[];
  public models?: BotModelModel[];
  public previewModel?: BotModelModel;
  public publishedModel?: BotModelModel;

  public static associations: {
    flows: HasMany;
    models: HasMany;
    previewModel: BelongsTo;
    publishedModel: BelongsTo;
  };
}

export function initBotModel(sequelize: Sequelize): typeof BotModel {
  BotModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      domain: {
        type: DataTypes.STRING(255),
        allowNull: true,
        defaultValue: "Default",
      },
      status: {
        type: DataTypes.ENUM(BotStatus.DRAFT, BotStatus.ACTIVE, BotStatus.INACTIVE),
        defaultValue: BotStatus.DRAFT,
      },
      settings: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      previewModelId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      publishedModelId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      tableName: "bots",
      timestamps: true,
    },
  );

  return BotModel;
}

export interface BotSettings {
  // NLU Configuration
  nlu: {
    provider: "rasa" | "dialogflow" | "luis" | "custom";
    endpoint?: string;
    apiKey?: string;
    modelName?: string;
    confidenceThreshold: number; // Minimum confidence for intent matching
    fallbackIntent: string; // Intent to use when confidence is too low
  };

  // Session Management
  session: {
    ttlMinutes: number; // Session timeout in minutes
    maxConcurrentSessions: number; // Per user limit
    persistContext: boolean; // Whether to persist context to database
    enableSessionResumption: boolean; // Allow resuming expired sessions
  };

  // Message Handling
  messaging: {
    enableTypingIndicator: boolean;
    typingDelayMs: number; // Delay before showing typing indicator
    maxMessageLength: number; // Maximum message length
    enableQuickReplies: boolean;
    enableRichMessages: boolean; // Cards, carousels, etc.
    defaultErrorMessage: string;
    defaultFallbackMessage: string;
  };

  // Flow Execution
  execution: {
    maxExecutionTimeMs: number; // Maximum time for flow execution
    maxLoopIterations: number; // Prevent infinite loops
    enableAsyncOperations: boolean;
    asyncTimeoutMs: number; // Timeout for async operations
    enableScriptExecution: boolean;
    scriptTimeoutMs: number; // Timeout for script nodes
  };

  // Integration Settings
  integrations: {
    webhook?: {
      url: string;
      secret?: string;
      events: string[]; // Which events to send
    };
    analytics?: {
      enabled: boolean;
      provider?: "google" | "mixpanel" | "custom";
      trackingId?: string;
    };
    logging?: {
      level: "debug" | "info" | "warn" | "error";
      enableChatHistory: boolean;
      retentionDays: number;
    };
  };

  // Security Settings
  security: {
    enableRateLimit: boolean;
    rateLimitPerMinute: number;
    enableInputValidation: boolean;
    allowedFileTypes: string[];
    maxFileSize: number; // In bytes
    enableContentFilter: boolean;
  };
}
