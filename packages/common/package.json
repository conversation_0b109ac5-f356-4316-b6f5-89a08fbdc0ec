{"name": "@neuratalk/common", "version": "1.0.0", "description": "Common shared utilities, types, metrics, and storage for the no-code chatbot platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npm run clean && tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "keywords": ["chatbot", "common", "types", "metrics", "storage", "typescript"], "author": "Chatbot Platform Team", "license": "MIT", "dependencies": {"aws-sdk": "^2.1467.0", "kafkajs": "^2.2.4", "prom-client": "^14.2.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.3.1", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0"}, "files": ["dist/**/*"]}