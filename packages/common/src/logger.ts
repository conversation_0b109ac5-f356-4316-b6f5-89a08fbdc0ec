import winston from "winston";
import morgan from "morgan";
import jwt from "jsonwebtoken";
import config from "./config";

const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

const colors = {
  error: "red",
  warn: "yellow",
  info: "green",
  http: "magenta",
  debug: "white",
};

winston.addColors(colors);

const format = winston.format.combine(
  winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss:ms" }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

const transports: winston.transport[] = [
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    ),
  }),
];

if (config.logging.file) {
  transports.push(
    new winston.transports.File({
      filename: config.logging.file,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    })
  );
}

const logger = winston.createLogger({
  level: config.logging.level,
  levels,
  format,
  transports,
});

// Custom Morgan tokens
morgan.token("username", (req: any) => {
  try {
    if (req.headers && req.headers.authorization) {
      const decoded: any = jwt.decode(req.headers["authorization"].substring(7));
      return decoded && decoded.loginId;
    } else {
      return req.body.loginId || req.body.username;
    }
  } catch (err) {
    logger.error("Error in logging username in access cdrs:", err);
    return "";
  }
});

morgan.token("event", (req: any) => {
  try {
    if (req.originalUrl.split("/")[2] == "users") {
      return req.params.userId ? req.params.userId : req.body.loginId;
    } else if (req.originalUrl.split("/")[2] == "apps") {
      return req.params.appId ? req.params.appId : req.body.id;
    } else if (req.originalUrl.split("/")[2] == "workflow") {
      return req.params.appId ? req.params.appId : req.body.appId;
    } else if (req.originalUrl.split("/")[2] == "pm") {
      return req.params.pluginId ? req.params.pluginId : req.body.pluginId;
    } else if (req.originalUrl.split("/")[2] == "plugins") {
      return req.files && req.files.toString();
    }
    return "";
  } catch (err) {
    logger.error("Error in logging event in access cdrs:", err);
    return "";
  }
});

morgan.token("process", () => {
  try {
    return String(process.pid);
  } catch (err) {
    logger.error("Error in logging event in access cdrs:", err);
    return "";
  }
});

morgan.token("transactionId", (req: any) => {
  try {
    return req.txnId;
  } catch (err) {
    logger.error("Error in logging event in access cdrs:", err);
    return "";
  }
});

const morganMiddleware = morgan(
  ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :username :event :process :transactionId',
  {
    stream: {
      write: (message) => {
        logger.info(message.trim());
      },
    },
  }
);

export { logger, morganMiddleware };