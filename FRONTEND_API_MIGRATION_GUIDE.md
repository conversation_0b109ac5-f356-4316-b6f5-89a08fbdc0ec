# Frontend API Migration Guide

This guide outlines the migration path from the previous knowledge unit-based API to the new multilingual FAQ and intent management API structure.

## Overview of Changes

The API has been completely restructured to support:

1. **Multilingual content** through dedicated translation models
2. **Category-based organization** for FAQs
3. **Improved entity management** with metadata support
4. **Dedicated utterance management** for intents

## Previous vs New Structure

### Previous Structure

```
KnowledgeUnit
├── FaqItem (questions, answer)
└── IntentItem (text, entities)
```

### New Structure

```
FaqCategory
└── FaqTranslation (questions, answer per language)

IntentItem
└── IntentUtteranceTranslation (text, entities per language)
```

**Note:** FaqItem and IntentUtterance are internal models and not directly exposed through the API.

## Key API Changes

### FAQ Management

#### Before:
```typescript
// Create a FAQ
const knowledgeUnit = await createKnowledgeUnit({
  name: "How to reset password",
  type: KnowledgeUnitType.FAQ,
  botId: "bot-123"
});

await createFaqItem({
  knowledgeUnitId: knowledgeUnit.id,
  questions: ["How do I reset my password?", "Forgot password help"],
  answer: "You can reset your password by clicking the 'Forgot Password' link."
});
```

#### After:
```typescript
// Create a FAQ category if needed
const category = await createFaqCategory({
  botId: "bot-123",
  name: "Account Management"
});

// Create FAQ translation (which automatically creates the FAQ item)
await createFaqTranslation({
  botId: "bot-123",
  categoryId: category.id,
  langId: "en-123",
  questions: ["How do I reset my password?", "Forgot password help"],
  answer: "You can reset your password by clicking the 'Forgot Password' link."
});

// Add another language
await createFaqTranslation({
  botId: "bot-123",
  categoryId: category.id,
  langId: "es-123",
  questions: ["¿Cómo restablezco mi contraseña?"],
  answer: "Puede restablecer su contraseña haciendo clic en el enlace 'Olvidé mi contraseña'."
});
```

### Intent Management

#### Before:
```typescript
// Create an intent
const knowledgeUnit = await createKnowledgeUnit({
  name: "book_flight",
  type: KnowledgeUnitType.INTENT,
  botId: "bot-123"
});

await createIntentItem({
  knowledgeUnitId: knowledgeUnit.id,
  text: "I want to book a flight to New York",
  entities: [
    { id: "destination", name: "destination", value: "New York" }
  ]
});
```

#### After:
```typescript
// Create an intent item
const intentItem = await createIntentItem({
  botId: "bot-123",
  name: "book_flight",
  description: "Intent for booking flight tickets"
});

// Create an entity
const entity = await createEntity({
  botId: "bot-123",
  intentId: intentItem.id,
  name: "destination",
  metadata: { type: "city" }
});

// Create utterance translation with entities (automatically creates the utterance)
await createIntentUtteranceTranslation({
  intentId: intentItem.id,
  langId: "en-123",
  text: "I want to book a flight to New York",
  entities: {
    destination: {
      value: "New York",
      start: 25,
      end: 33
    }
  }
});
```

## Step-by-Step Migration Guide

### 1. Update API Imports

```typescript
// Before
import {
  useGetKnowledgeUnitsQuery,
  useCreateKnowledgeUnitMutation,
  // etc.
} from '@/client-hooks';

// After
import {
  useGetFaqCategoriesQuery,
  useGetFaqsByCategoryAndLanguageQuery,
  useGetFaqTranslationsQuery,
  useCreateFaqCategoryMutation,
  // etc.
} from '@/client-hooks';
```

### 2. Migrate FAQ Management

#### Fetching FAQs:

```typescript
// Before
const { data: faqUnits } = useGetKnowledgeUnitsQuery({
  filter: { type: KnowledgeUnitType.FAQ, botId: currentBotId }
});

// After
// Step 1: Get categories
const { data: categories } = useGetFaqCategoriesQuery({
  filter: { botId: currentBotId }
});

// Step 2: Get FAQs with translations for a specific category and language
const { data: faqsWithTranslations } = useGetFaqsByCategoryAndLanguageQuery({
  categoryId: selectedCategoryId,
  langId: currentLanguageId
});
```

#### Creating FAQs:

```typescript
// Before
const [createKnowledgeUnit] = useCreateKnowledgeUnitMutation();
const [createFaqItem] = useCreateFaqItemMutation();

const handleCreateFaq = async (data) => {
  const unit = await createKnowledgeUnit({
    name: data.name,
    type: KnowledgeUnitType.FAQ,
    botId: currentBotId
  }).unwrap();
  
  await createFaqItem({
    knowledgeUnitId: unit.data.id,
    questions: data.questions,
    answer: data.answer
  }).unwrap();
};

// After
const [createFaqCategory] = useCreateFaqCategoryMutation();
const [createFaqTranslation] = useCreateFaqTranslationMutation();

const handleCreateFaq = async (data) => {
  // Step 1: Create or use existing category
  let categoryId = data.categoryId;
  if (!categoryId) {
    const categoryResult = await createFaqCategory({
      botId: currentBotId,
      name: data.categoryName
    }).unwrap();
    categoryId = categoryResult.data.id;
  }
  
  // Create FAQ translation (which automatically creates the FAQ item)
  await createFaqTranslation({
    botId: currentBotId,
    categoryId: categoryId,
    flowId: data.flowId, // Optional
    langId: data.langId,
    questions: data.questions,
    answer: data.answer
  }).unwrap();
};
```

### 3. Migrate Intent Management

#### Fetching Intents:

```typescript
// Before
const { data: intentUnits } = useGetKnowledgeUnitsQuery({
  filter: { type: KnowledgeUnitType.INTENT, botId: currentBotId }
});

// After
// Step 1: Get intent items
const { data: intentItems } = useGetIntentItemsQuery({
  filter: { botId: currentBotId }
});

// Step 2: Get utterance translations for a specific intent and language
const { data: translations } = useGetIntentUtteranceTranslationsQuery({
  filter: { intentId: selectedIntentId, langId: currentLanguageId }
});
```

#### Creating Intents:

```typescript
// Before
const [createKnowledgeUnit] = useCreateKnowledgeUnitMutation();
const [createIntentItem] = useCreateIntentItemMutation();

const handleCreateIntent = async (data) => {
  const unit = await createKnowledgeUnit({
    name: data.name,
    type: KnowledgeUnitType.INTENT,
    botId: currentBotId
  }).unwrap();
  
  await createIntentItem({
    knowledgeUnitId: unit.data.id,
    text: data.text,
    entities: data.entities
  }).unwrap();
};

// After
const [createIntentItem] = useCreateIntentItemMutation();
const [createIntentUtteranceTranslation] = useCreateIntentUtteranceTranslationMutation();
const [createEntity] = useCreateEntityMutation();

const handleCreateIntent = async (data) => {
  // Step 1: Create intent item
  const intentResult = await createIntentItem({
    botId: currentBotId,
    name: data.name,
    description: data.description,
    flowId: data.flowId // Optional
  }).unwrap();
  
  // Create utterance translation (which automatically creates the utterance)
  await createIntentUtteranceTranslation({
    intentId: intentResult.data.id,
    langId: data.langId,
    text: data.text,
    entities: data.entities
  }).unwrap();
  
  // Step 4: Create entities if needed
  for (const entityName of Object.keys(data.entities)) {
    await createEntity({
      botId: currentBotId,
      intentId: intentResult.data.id,
      name: entityName
    }).unwrap();
  }
};
```

### 4. Update UI Components

#### FAQ Management UI:

```tsx
// Before
<Form onSubmit={handleSubmit}>
  <Input name="name" label="FAQ Name" />
  <TextArea name="questions" label="Questions" />
  <TextArea name="answer" label="Answer" />
  <Button type="submit">Save</Button>
</Form>

// After
<Form onSubmit={handleSubmit}>
  <Select 
    name="categoryId" 
    label="Category" 
    options={categories.map(c => ({ value: c.id, label: c.name }))}
    onCreateOption={handleCreateCategory}
  />
  
  <Select 
    name="langId" 
    label="Language" 
    options={languages.map(l => ({ value: l.id, label: l.name }))}
  />
  
  <TextArea name="questions" label="Questions" />
  <TextArea name="answer" label="Answer" />
  <Button type="submit">Save</Button>
</Form>
```

#### Intent Management UI:

```tsx
// Before
<Form onSubmit={handleSubmit}>
  <Input name="name" label="Intent Name" />
  <Input name="text" label="Example Text" />
  <EntityEditor name="entities" label="Entities" />
  <Button type="submit">Save</Button>
</Form>

// After
<Form onSubmit={handleSubmit}>
  <Input name="name" label="Intent Name" />
  <TextArea name="description" label="Description" />
  
  <Select 
    name="langId" 
    label="Language" 
    options={languages.map(l => ({ value: l.id, label: l.name }))}
  />
  
  <Input name="text" label="Example Text" />
  <EntityEditor 
    name="entities" 
    label="Entities" 
    availableEntities={entities}
    onCreateEntity={handleCreateEntity}
  />
  <Button type="submit">Save</Button>
</Form>
```

## Common Patterns

### 1. Working with Multiple Languages

```typescript
// Get available languages
const { data: languages } = useGetLanguagesQuery({});

// Get bot-specific languages
const { data: botLanguages } = useGetBotLanguagesQuery({
  filter: { botId: currentBotId }
});

// Create content in multiple languages
const createMultilingualFaq = async (data, languages) => {
  // Create translations for each language (each creates its own FAQ item)
  const translationPromises = languages.map(lang => 
    createFaqTranslation({
      botId: currentBotId,
      categoryId: data.categoryId,
      langId: lang.id,
      questions: data.questions[lang.code] || [],
      answer: data.answers[lang.code] || ''
    }).unwrap()
  );
  
  await Promise.all(translationPromises);
};
```

### 2. Assigning Flows to Intents

```typescript
const [assignFlowToIntent] = useAssignFlowToIntentMutation();

// Assign a flow to an intent
const handleAssignFlow = async (intentId, flowId) => {
  await assignFlowToIntent({
    intentId,
    flowId
  }).unwrap();
};
```

## Breaking Changes

1. **KnowledgeUnit Removal**: The `KnowledgeUnit` entity has been completely removed. All references need to be updated to use the new specific entity types.

2. **Multilingual Requirement**: The new API requires explicit language selection for all content. Content is now stored in language-specific translation entities.

3. **Entity Structure**: Entity handling has changed significantly:
   - Entities are now linked directly to intents
   - Entities now support metadata
   - Entity references in utterances use a different format

4. **API Path Changes**: All API paths have been updated to reflect the new entity structure.

5. **Direct Item Management**: FaqItem and IntentUtterance are not directly exposed through the API:
   - FAQ items are created automatically when creating a FAQ translation
   - Intent utterances are created automatically when creating an utterance translation

## Conclusion

This migration represents a significant architectural improvement that enables:

- Better organization with categories
- Full multilingual support
- More detailed entity management
- Improved separation of concerns

While the migration requires significant changes to frontend code, it enables much richer functionality and better scalability for the application.