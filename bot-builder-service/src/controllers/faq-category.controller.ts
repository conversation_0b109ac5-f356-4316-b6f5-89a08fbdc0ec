import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { CreateFaqCategoryRequest, UpdateFaqCategoryRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { AppContext } from "../types/context.types";

export class FaqCategoryController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/faq-categories:
   *   post:
   *     summary: Create a new FAQ category
   *     tags: [FAQ Categories]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateFaqCategoryRequest'
   *     responses:
   *       201:
   *         description: FAQ category created successfully
   */
  public create = async (
    req: Request<any, any, CreateFaqCategoryRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const faqCategory = await this.models.FaqCategory.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`FAQ category created: ${faqCategory.id}`);
      res.status(201).json(successResponse(faqCategory));
    } catch (error) {
      logger.error("Error creating FAQ category:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-categories:
   *   get:
   *     summary: Get all FAQ categories
   *     tags: [FAQ Categories]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *       - in: query
   *         name: botId
   *         schema: { type: string, format: uuid }
   *     responses:
   *       200:
   *         description: List of FAQ categories
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.FaqCategory, req.query, ["name"]);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching FAQ categories:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ categories",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-categories/{id}:
   *   get:
   *     summary: Get FAQ category by ID
   *     tags: [FAQ Categories]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: FAQ category object
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const faqCategory = await this.models.FaqCategory.findOne({
        where: { id },
      });

      if (!faqCategory) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ category not found" }));
        return;
      }

      res.json(successResponse(faqCategory));
    } catch (error) {
      logger.error(`Error fetching FAQ category ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch FAQ category" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-categories/{id}:
   *   put:
   *     summary: Update an FAQ category
   *     tags: [FAQ Categories]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateFaqCategoryRequest'
   *     responses:
   *       200:
   *         description: FAQ category updated successfully
   */
  public update = async (
    req: Request<UuidParams, any, UpdateFaqCategoryRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.FaqCategory.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id },
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ category not found" }));
        return;
      }

      const faqCategory = await this.models.FaqCategory.findByPk(id);
      logger.info(`FAQ category updated: ${id}`);

      res.json(successResponse(faqCategory));
    } catch (error) {
      logger.error(`Error updating FAQ category ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-categories/{id}:
   *   delete:
   *     summary: Delete an FAQ category
   *     tags: [FAQ Categories]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: FAQ category deleted successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.FaqCategory.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ category not found" }));
        return;
      }

      logger.info(`FAQ category deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting FAQ category ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to delete FAQ category",
        }),
      );
    }
  };
}
