import { Request, Response } from "express";
import { AppController } from "../controllers/app.controller";
import { getStudioAppsService } from "api_gw";
import { ApiResponse } from "@neuratalk/common";
import { AppIdParam, UpdateAppRequest } from "../schemas";

jest.mock("api_gw", () => ({
  getStudioAppsService: jest.fn().mockReturnValue({
    createAppInfo: jest.fn(),
    updateAppInfo: jest.fn(),
    getAllApps: jest.fn(),
  }),
}));


jest.mock("@neuratalk/common", () => ({
  ...jest.requireActual("@neuratalk/common"),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe("AppController", () => {
  let controller: AppController;
  let mockStudioAppsService: any;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {

    controller = new AppController();
    mockStudioAppsService = getStudioAppsService();


    mockReq = {
      body: {},
      params: {},
      query: {},
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      // Add headersSent property to simulate scenarios where the service sends the response
      headersSent: false,
    };

    jest.clearAllMocks();
  });

  describe("createApp", () => {
    it("should create an app and return 201 on success", async () => {
      const mockAppPayload = { name: "New Test App" };
      const mockAppResponse = { id: "app-uuid-123", ...mockAppPayload };
      mockReq.body = mockAppPayload;

      mockStudioAppsService.createAppInfo.mockResolvedValue(mockAppResponse);

      await controller.createApp(mockReq as Request, mockRes as Response);


      expect(mockStudioAppsService.createAppInfo).toHaveBeenCalledWith(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockAppResponse,
        timestamp: expect.any(Date),
      } as ApiResponse);
    });

    it("should return 500 if the service throws an error", async () => {
      const error = new Error("Service unavailable");
      mockStudioAppsService.createAppInfo.mockRejectedValue(error);

      await controller.createApp(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: { code: "INTERNAL_ERROR", message: "Failed to create app" },
        timestamp: expect.any(Date),
      } as ApiResponse);
    });

    it("should not send a response if headers were already sent by the service", async () => {

      mockStudioAppsService.createAppInfo.mockImplementation((req: Request, res: Response) => {
        res.headersSent = true;
        return Promise.resolve({});
      });

      await controller.createApp(mockReq as Request, mockRes as Response);


      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });


  describe("updateApp", () => {
    let updateAppMockReq: Request<AppIdParam, any, UpdateAppRequest>;

    beforeEach(() => {
      updateAppMockReq = {
        params: { appId: "app-uuid-123" },
        body: { name: "Updated Test App" },
      } as Request<AppIdParam, any, UpdateAppRequest>;
    });

    it("should update an app and return 200 on success", async () => {
      const mockAppResponse = { id: "app-uuid-123", name: "Updated Test App" };
      mockStudioAppsService.updateAppInfo.mockResolvedValue(mockAppResponse);

      await controller.updateApp(updateAppMockReq, mockRes as Response);

      expect(mockStudioAppsService.updateAppInfo).toHaveBeenCalledWith(updateAppMockReq, mockRes);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockAppResponse,
        timestamp: expect.any(Date),
      } as ApiResponse);
    });

    it("should return 500 if the service fails to update", async () => {
      const error = new Error("Update failed");
      mockStudioAppsService.updateAppInfo.mockRejectedValue(error);

      await controller.updateApp(updateAppMockReq, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: { code: "INTERNAL_ERROR", message: "Failed to update app" },
        timestamp: expect.any(Date),
      } as ApiResponse);
    });
  });


  describe("getApps", () => {
    it("should retrieve a list of apps and return 200 on success", async () => {
      const mockAppsList = [{ id: "app-1" }, { id: "app-2" }];
      mockReq.query = { page: "1", limit: "10" };
      mockStudioAppsService.getAllApps.mockResolvedValue(mockAppsList);

      await controller.getApps(mockReq as Request, mockRes as Response);

      expect(mockStudioAppsService.getAllApps).toHaveBeenCalledWith(mockReq, mockRes);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockAppsList,
        timestamp: expect.any(Date),
      } as ApiResponse);
    });

    it("should return 500 if the service fails to retrieve apps", async () => {
      const error = new Error("Failed to fetch");
      mockStudioAppsService.getAllApps.mockRejectedValue(error);

      await controller.getApps(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: { code: "INTERNAL_ERROR", message: "Failed to get apps" },
        timestamp: expect.any(Date),
      } as ApiResponse);
    });
  });
});