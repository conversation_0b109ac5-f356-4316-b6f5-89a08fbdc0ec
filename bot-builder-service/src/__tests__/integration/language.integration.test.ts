import request from "supertest";
import { TestContext, createTestApp } from "./test-setup";
import { createLanguageRoutes } from "../../routers/language.routes";

describe("Language Integration Tests", () => {
  let testContext: TestContext;

  beforeAll(async () => {
    testContext = await createTestApp();
    testContext.app.use("/", createLanguageRoutes(testContext.context));
  });

  afterAll(async () => {
    await testContext.db.disconnect();
  });

  beforeEach(async () => {
    await testContext.db.sequelize.sync({ force: true });
  });

  describe("POST /languages", () => {
    it("should create a language successfully", async () => {
      const languageData = {
        name: "English",
        code: "en",
      };

      const response = await request(testContext.app)
        .post("/languages")
        .send(languageData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        name: "English",
        code: "en",
      });
      expect(response.body.data.id).toBeDefined();
    });

    it("should return validation error for invalid data", async () => {
      const response = await request(testContext.app)
        .post("/languages")
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe("GET /languages", () => {
    beforeEach(async () => {
      await testContext.context.db.models.Language.bulkCreate([
        { name: "English", code: "en" },
        { name: "Spanish", code: "es" },
      ]);
    });

    it("should retrieve all languages with pagination", async () => {
      const response = await request(testContext.app)
        .get("/languages")
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(2);
      expect(response.body.data.pagination).toBeDefined();
    });

    it("should filter languages by search", async () => {
      const response = await request(testContext.app)
        .get("/languages")
        .query({ search: "English" })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(1);
      expect(response.body.data.items[0].name).toBe("English");
    });
  });

  describe("GET /languages/:id", () => {
    let languageId: string;

    beforeEach(async () => {
      const language = await testContext.context.db.models.Language.create({
        name: "French",
        code: "fr",
      });
      languageId = language.id;
    });

    it("should retrieve language by ID", async () => {
      const response = await request(testContext.app)
        .get(`/languages/${languageId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe("French");
      expect(response.body.data.code).toBe("fr");
    });

    it("should return 404 for non-existent language", async () => {
      const response = await request(testContext.app)
        .get("/languages/non-existent-id")
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe("Language not found");
    });
  });
});