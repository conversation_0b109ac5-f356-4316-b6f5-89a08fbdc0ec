import request from "supertest";
import { TestContext, createTestApp } from "./test-setup";
import { createLanguageRoutes } from "../../routers/language.routes";
import { BotStatus } from "@neuratalk/bot-store";

describe("Bot Language Integration Tests", () => {
  let testContext: TestContext;
  let botId: string;
  let langId: string;

  beforeAll(async () => {
    testContext = await createTestApp();
    const { createBotLanguageRoutes } = require("../../routers/bot.routes");
    testContext.app.use("/", createBotLanguageRoutes(testContext.context));
  });

  afterAll(async () => {
    await testContext.db.disconnect();
  });

  beforeEach(async () => {
    await testContext.db.sync({ force: true });
    
    const bot = await testContext.db.models.Bot.create({
      name: "Test Bot",
      description: "Test bot description",
      status: BotStatus.DRAFT,
      createdBy: "test-user-id",
      updatedBy: "test-user-id",
    });
    botId = bot.id;

    const language = await testContext.db.models.Language.create({
      name: "English",
      code: "en",
    });
    langId = language.id;
  });

  describe("POST /bot-languages", () => {
    it("should assign language to bot successfully", async () => {
      const botLanguageData = {
        botId,
        langId,
        isDefault: true,
      };

      const response = await request(testContext.app)
        .post("/bot-languages")
        .send(botLanguageData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        botId,
        langId,
        isDefault: true,
        createdBy: "test-user-id",
      });
    });

    it("should set new default and unset previous default", async () => {
      await testContext.context.db.models.BotLanguage.create({
        botId,
        langId,
        isDefault: true,
        createdBy: "test-user-id",
      });

      const newLanguage = await testContext.context.db.models.Language.create({
        name: "Spanish",
        code: "es",
      });

      const response = await request(testContext.app)
        .post("/bot-languages")
        .send({
          botId,
          langId: newLanguage.id,
          isDefault: true,
        })
        .expect(201);

      expect(response.body.success).toBe(true);

      const oldDefault = await testContext.context.db.models.BotLanguage.findOne({
        where: { botId, langId, isDefault: true },
      });
      expect(oldDefault).toBeNull();
    });
  });

  describe("GET /bot-languages", () => {
    beforeEach(async () => {
      await testContext.context.db.models.BotLanguage.bulkCreate([
        { botId, langId, isDefault: true, createdBy: "test-user-id" },
      ]);
    });

    it("should retrieve all bot languages", async () => {
      const response = await request(testContext.app)
        .get("/bot-languages")
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(1);
      expect(response.body.data.items[0].botId).toBe(botId);
    });

    it("should support pagination", async () => {
      const response = await request(testContext.app)
        .get("/bot-languages")
        .query({ page: 1, limit: 5 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination).toBeDefined();
    });
  });

  describe("GET /bot-languages/:id", () => {
    let botLanguageId: string;

    beforeEach(async () => {
      const botLanguage = await testContext.context.db.models.BotLanguage.create({
        botId,
        langId,
        isDefault: false,
        createdBy: "test-user-id",
      });
      botLanguageId = botLanguage.id;
    });

    it("should retrieve bot language by ID", async () => {
      const response = await request(testContext.app)
        .get(`/bot-languages/${botLanguageId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(botLanguageId);
      expect(response.body.data.botId).toBe(botId);
    });

    it("should return 404 for non-existent bot language", async () => {
      const response = await request(testContext.app)
        .get("/bot-languages/non-existent-id")
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe("DELETE /bot-languages/:id", () => {
    let botLanguageId: string;

    beforeEach(async () => {
      const botLanguage = await testContext.context.db.models.BotLanguage.create({
        botId,
        langId,
        isDefault: false,
        createdBy: "test-user-id",
      });
      botLanguageId = botLanguage.id;
    });

    it("should delete bot language successfully", async () => {
      await request(testContext.app)
        .delete(`/bot-languages/${botLanguageId}`)
        .expect(204);

      const deleted = await testContext.context.db.models.BotLanguage.findByPk(botLanguageId);
      expect(deleted).toBeNull();
    });

    it("should return 404 for non-existent bot language", async () => {
      const response = await request(testContext.app)
        .delete("/bot-languages/non-existent-id")
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });
});