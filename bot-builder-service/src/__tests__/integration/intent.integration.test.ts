import request from "supertest";
import { TestContext, createTestApp, mockAuthMiddleware } from "./test-setup";

describe("Intent Integration Tests", () => {
  let testContext: TestContext;
  let botId: string;
  let langId: string;
  let flowId: string;

  beforeAll(async () => {
    testContext = await createTestApp();
    jest.doMock("../../middleware/auth.middleware", () => ({
      authMiddleware: mockAuthMiddleware,
    }));
  });

  afterAll(async () => {
    await testContext.db.close();
  });

  beforeEach(async () => {
    await testContext.db.sync({ force: true });
    
    const bot = await testContext.context.db.models.Bot.create({
      name: "Test Bot",
      description: "Test bot description",
      createdBy: "test-user-id",
    });
    botId = bot.id;

    const language = await testContext.context.db.models.Language.create({
      name: "English",
      code: "en",
      isActive: true,
    });
    langId = language.id;

    const flow = await testContext.context.db.models.Flow.create({
      name: "Test Flow",
      description: "Test flow description",
      botId,
      createdBy: "test-user-id",
    });
    flowId = flow.id;
  });

  describe("Intent Items", () => {
    describe("POST /intent-items", () => {
      it("should create intent item successfully", async () => {
        const intentData = {
          name: "greeting",
          description: "Greeting intent",
          botId,
        };

        const response = await request(testContext.app)
          .post("/intent-items")
          .send(intentData)
          .expect(201);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toMatchObject({
          name: "greeting",
          description: "Greeting intent",
          botId,
        });
      });

      it("should return validation error for invalid data", async () => {
        const response = await request(testContext.app)
          .post("/intent-items")
          .send({})
          .expect(400);

        expect(response.body.success).toBe(false);
      });
    });

    describe("GET /intent-items", () => {
      beforeEach(async () => {
        await testContext.context.db.models.IntentItems.bulkCreate([
          { name: "greeting", description: "Greeting intent", botId },
          { name: "goodbye", description: "Goodbye intent", botId },
        ]);
      });

      it("should retrieve all intent items", async () => {
        const response = await request(testContext.app)
          .get("/intent-items")
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.items).toHaveLength(2);
      });

      it("should support pagination", async () => {
        const response = await request(testContext.app)
          .get("/intent-items")
          .query({ page: 1, limit: 1 })
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.items).toHaveLength(1);
        expect(response.body.data.pagination.totalPages).toBe(2);
      });
    });

    describe("GET /intent-items/:id", () => {
      let intentId: string;

      beforeEach(async () => {
        const intent = await testContext.context.db.models.IntentItems.create({
          name: "test_intent",
          description: "Test intent",
          botId,
        });
        intentId = intent.id;
      });

      it("should retrieve intent item by ID", async () => {
        const response = await request(testContext.app)
          .get(`/intent-items/${intentId}`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.name).toBe("test_intent");
      });

      it("should return 404 for non-existent intent", async () => {
        const response = await request(testContext.app)
          .get("/intent-items/non-existent-id")
          .expect(404);

        expect(response.body.success).toBe(false);
      });
    });

    describe("PUT /intent-items/:id", () => {
      let intentId: string;

      beforeEach(async () => {
        const intent = await testContext.context.db.models.IntentItems.create({
          name: "original_intent",
          description: "Original description",
          botId,
        });
        intentId = intent.id;
      });

      it("should update intent item successfully", async () => {
        const updateData = {
          name: "updated_intent",
          description: "Updated description",
        };

        const response = await request(testContext.app)
          .put(`/intent-items/${intentId}`)
          .send(updateData)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.name).toBe("updated_intent");
      });
    });

    describe("DELETE /intent-items/:id", () => {
      let intentId: string;

      beforeEach(async () => {
        const intent = await testContext.context.db.models.IntentItems.create({
          name: "to_delete",
          description: "Intent to delete",
          botId,
        });
        intentId = intent.id;
      });

      it("should delete intent item successfully", async () => {
        await request(testContext.app)
          .delete(`/intent-items/${intentId}`)
          .expect(204);

        const deleted = await testContext.context.db.models.IntentItems.findByPk(intentId);
        expect(deleted).toBeNull();
      });
    });

    describe("POST /intent-items/assign-flow", () => {
      let intentId: string;

      beforeEach(async () => {
        const intent = await testContext.context.db.models.IntentItems.create({
          name: "flow_intent",
          description: "Intent for flow assignment",
          botId,
        });
        intentId = intent.id;
      });

      it("should assign flow to intent successfully", async () => {
        const assignData = {
          intentId,
          flowId,
        };

        const response = await request(testContext.app)
          .post("/intent-items/assign-flow")
          .send(assignData)
          .expect(200);

        expect(response.body.success).toBe(true);
        
        const updatedIntent = await testContext.context.db.models.IntentItems.findByPk(intentId);
        expect(updatedIntent?.flowId).toBe(flowId);
      });
    });
  });

  describe("Intent Utterance Translations", () => {
    let intentId: string;
    let utteranceId: string;

    beforeEach(async () => {
      const intent = await testContext.context.db.models.IntentItems.create({
        name: "test_intent",
        description: "Test intent",
        botId,
      });
      intentId = intent.id;

      const utterance = await testContext.context.db.models.IntentUtterance.create({
        intentId,
        createdBy: "test-user-id",
      });
      utteranceId = utterance.id;
    });

    describe("POST /intent/:intentId/lang/:langId/intent-utterance", () => {
      it("should create utterance translation successfully", async () => {
        const utteranceData = {
          text: "Hello there",
        };

        const response = await request(testContext.app)
          .post(`/intent/${intentId}/lang/${langId}/intent-utterance`)
          .send(utteranceData)
          .expect(201);

        expect(response.body.success).toBe(true);
        expect(response.body.data.text).toBe("Hello there");
      });
    });

    describe("GET /intent/:intentId/lang/:langId/intent-utterance", () => {
      beforeEach(async () => {
        await testContext.context.db.models.UtteranceTranslation.create({
          utteranceId,
          langId,
          text: "Test utterance",
        });
      });

      it("should retrieve utterances for intent and language", async () => {
        const response = await request(testContext.app)
          .get(`/intent/${intentId}/lang/${langId}/intent-utterance`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.items).toHaveLength(1);
        expect(response.body.data.items[0].text).toBe("Test utterance");
      });
    });

    describe("GET /intent-utterance/:id", () => {
      let translationId: string;

      beforeEach(async () => {
        const translation = await testContext.context.db.models.UtteranceTranslation.create({
          utteranceId,
          langId,
          text: "Specific utterance",
        });
        translationId = translation.id;
      });

      it("should retrieve utterance translation by ID", async () => {
        const response = await request(testContext.app)
          .get(`/intent-utterance/${translationId}`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.text).toBe("Specific utterance");
      });
    });

    describe("GET /utterance/:utteranceId/lang/:langId/translation", () => {
      beforeEach(async () => {
        await testContext.context.db.models.UtteranceTranslation.create({
          utteranceId,
          langId,
          text: "Translation text",
        });
      });

      it("should retrieve translation by utterance and language", async () => {
        const response = await request(testContext.app)
          .get(`/utterance/${utteranceId}/lang/${langId}/translation`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.text).toBe("Translation text");
      });
    });

    describe("PUT /intent-utterance/:id", () => {
      let translationId: string;

      beforeEach(async () => {
        const translation = await testContext.context.db.models.UtteranceTranslation.create({
          utteranceId,
          langId,
          text: "Original text",
        });
        translationId = translation.id;
      });

      it("should update utterance translation successfully", async () => {
        const updateData = {
          text: "Updated text",
        };

        const response = await request(testContext.app)
          .put(`/intent-utterance/${translationId}`)
          .send(updateData)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.text).toBe("Updated text");
      });
    });

    describe("DELETE /intent-utterance/:id", () => {
      let translationId: string;

      beforeEach(async () => {
        const translation = await testContext.context.db.models.UtteranceTranslation.create({
          utteranceId,
          langId,
          text: "To delete",
        });
        translationId = translation.id;
      });

      it("should delete utterance translation successfully", async () => {
        await request(testContext.app)
          .delete(`/intent-utterance/${translationId}`)
          .expect(204);

        const deleted = await testContext.context.db.models.UtteranceTranslation.findByPk(translationId);
        expect(deleted).toBeNull();
      });
    });
  });
});