import request from "supertest";
import { TestContext, createTestApp, mockAuthMiddleware } from "./test-setup";

describe("Training Integration Tests", () => {
  let testContext: TestContext;
  let botId: string;
  let langId: string;

  beforeAll(async () => {
    testContext = await createTestApp();
    jest.doMock("../../middleware/auth.middleware", () => ({
      authMiddleware: mockAuthMiddleware,
    }));
  });

  afterAll(async () => {
    await testContext.db.close();
  });

  beforeEach(async () => {
    await testContext.db.sync({ force: true });
    
    const bot = await testContext.context.db.models.Bot.create({
      name: "Training Test Bot",
      description: "Bot for training tests",
      createdBy: "test-user-id",
    });
    botId = bot.id;

    const language = await testContext.context.db.models.Language.create({
      name: "English",
      code: "en",
      isActive: true,
    });
    langId = language.id;
  });

  describe("Complete Training Data Workflow", () => {
    it("should create complete training data set", async () => {
      // Create FAQ Category
      const faqCategoryResponse = await request(testContext.app)
        .post("/faq-categories")
        .send({
          name: "General",
          description: "General questions",
          botId,
        })
        .expect(201);

      const categoryId = faqCategoryResponse.body.data.id;

      // Create FAQ Item
      const faqItem = await testContext.context.db.models.FaqItems.create({
        categoryId,
        createdBy: "test-user-id",
      });

      // Create FAQ Translation
      const faqTranslationResponse = await request(testContext.app)
        .post("/faq-translations")
        .send({
          faqId: faqItem.id,
          langId,
          question: "What are your hours?",
          answer: "We are open 24/7",
        })
        .expect(201);

      // Create Intent
      const intentResponse = await request(testContext.app)
        .post("/intent-items")
        .send({
          name: "ask_hours",
          description: "Ask about business hours",
          botId,
        })
        .expect(201);

      const intentId = intentResponse.body.data.id;

      // Create Intent Utterance
      const utteranceResponse = await request(testContext.app)
        .post(`/intent/${intentId}/lang/${langId}/intent-utterance`)
        .send({
          text: "What time do you open?",
        })
        .expect(201);

      // Create Entity
      const entityResponse = await request(testContext.app)
        .post("/entities")
        .send({
          name: "time_period",
          description: "Time periods",
          botId,
          entityType: "custom",
          values: ["morning", "afternoon", "evening", "night"],
        })
        .expect(201);

      // Verify all data was created
      expect(faqCategoryResponse.body.data.name).toBe("General");
      expect(faqTranslationResponse.body.data.question).toBe("What are your hours?");
      expect(intentResponse.body.data.name).toBe("ask_hours");
      expect(utteranceResponse.body.data.text).toBe("What time do you open?");
      expect(entityResponse.body.data.name).toBe("time_period");
    });

    it("should handle cross-entity relationships", async () => {
      // Create Flow
      const flow = await testContext.context.db.models.Flow.create({
        name: "Hours Flow",
        description: "Flow for handling hours questions",
        botId,
        createdBy: "test-user-id",
      });

      // Create Intent
      const intentResponse = await request(testContext.app)
        .post("/intent-items")
        .send({
          name: "business_hours",
          description: "Business hours intent",
          botId,
        })
        .expect(201);

      const intentId = intentResponse.body.data.id;

      // Assign Flow to Intent
      const assignResponse = await request(testContext.app)
        .post("/intent-items/assign-flow")
        .send({
          intentId,
          flowId: flow.id,
        })
        .expect(200);

      expect(assignResponse.body.success).toBe(true);

      // Verify relationship
      const updatedIntent = await testContext.context.db.models.IntentItems.findByPk(intentId);
      expect(updatedIntent?.flowId).toBe(flow.id);
    });

    it("should support multilingual training data", async () => {
      // Create additional language
      const spanishLang = await testContext.context.db.models.Language.create({
        name: "Spanish",
        code: "es",
        isActive: true,
      });

      // Create Intent
      const intentResponse = await request(testContext.app)
        .post("/intent-items")
        .send({
          name: "greeting",
          description: "Greeting intent",
          botId,
        })
        .expect(201);

      const intentId = intentResponse.body.data.id;

      // Create English utterance
      const englishUtteranceResponse = await request(testContext.app)
        .post(`/intent/${intentId}/lang/${langId}/intent-utterance`)
        .send({
          text: "Hello",
        })
        .expect(201);

      // Create Spanish utterance
      const spanishUtteranceResponse = await request(testContext.app)
        .post(`/intent/${intentId}/lang/${spanishLang.id}/intent-utterance`)
        .send({
          text: "Hola",
        })
        .expect(201);

      // Verify both utterances exist
      const englishUtterances = await request(testContext.app)
        .get(`/intent/${intentId}/lang/${langId}/intent-utterance`)
        .expect(200);

      const spanishUtterances = await request(testContext.app)
        .get(`/intent/${intentId}/lang/${spanishLang.id}/intent-utterance`)
        .expect(200);

      expect(englishUtterances.body.data.items).toHaveLength(1);
      expect(spanishUtterances.body.data.items).toHaveLength(1);
      expect(englishUtterances.body.data.items[0].text).toBe("Hello");
      expect(spanishUtterances.body.data.items[0].text).toBe("Hola");
    });
  });

  describe("Training Data Validation", () => {
    it("should validate entity references in intents", async () => {
      // Create entity first
      const entityResponse = await request(testContext.app)
        .post("/entities")
        .send({
          name: "product",
          description: "Product entity",
          botId,
          entityType: "custom",
          values: ["laptop", "phone", "tablet"],
        })
        .expect(201);

      // Create intent that could reference the entity
      const intentResponse = await request(testContext.app)
        .post("/intent-items")
        .send({
          name: "product_inquiry",
          description: "Product inquiry intent",
          botId,
        })
        .expect(201);

      const intentId = intentResponse.body.data.id;

      // Create utterance with entity reference
      const utteranceResponse = await request(testContext.app)
        .post(`/intent/${intentId}/lang/${langId}/intent-utterance`)
        .send({
          text: "I want to buy a laptop",
        })
        .expect(201);

      expect(utteranceResponse.body.success).toBe(true);
    });

    it("should handle training data consistency", async () => {
      // Create FAQ category
      const categoryResponse = await request(testContext.app)
        .post("/faq-categories")
        .send({
          name: "Products",
          description: "Product related questions",
          botId,
        })
        .expect(201);

      const categoryId = categoryResponse.body.data.id;

      // Create multiple FAQ items in the same category
      const faqItem1 = await testContext.context.db.models.FaqItems.create({
        categoryId,
        createdBy: "test-user-id",
      });

      const faqItem2 = await testContext.context.db.models.FaqItems.create({
        categoryId,
        createdBy: "test-user-id",
      });

      // Create translations for both FAQ items
      await request(testContext.app)
        .post("/faq-translations")
        .send({
          faqId: faqItem1.id,
          langId,
          question: "What products do you sell?",
          answer: "We sell laptops, phones, and tablets",
        })
        .expect(201);

      await request(testContext.app)
        .post("/faq-translations")
        .send({
          faqId: faqItem2.id,
          langId,
          question: "Do you have warranty?",
          answer: "Yes, all products come with 1-year warranty",
        })
        .expect(201);

      // Retrieve FAQs by category
      const faqsResponse = await request(testContext.app)
        .get("/faqs")
        .query({ categoryId, langId })
        .expect(200);

      expect(faqsResponse.body.data).toHaveLength(2);
    });
  });

  describe("Training Data Performance", () => {
    it("should handle bulk training data operations", async () => {
      const startTime = Date.now();

      // Create multiple entities concurrently
      const entityPromises = Array.from({ length: 5 }, (_, i) =>
        request(testContext.app)
          .post("/entities")
          .send({
            name: `entity_${i}`,
            description: `Entity ${i}`,
            botId,
            entityType: "custom",
            values: [`value_${i}_1`, `value_${i}_2`],
          })
      );

      const entityResponses = await Promise.all(entityPromises);
      
      // Create multiple intents concurrently
      const intentPromises = Array.from({ length: 5 }, (_, i) =>
        request(testContext.app)
          .post("/intent-items")
          .send({
            name: `intent_${i}`,
            description: `Intent ${i}`,
            botId,
          })
      );

      const intentResponses = await Promise.all(intentPromises);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify all operations succeeded
      entityResponses.forEach(response => {
        expect(response.status).toBe(201);
      });

      intentResponses.forEach(response => {
        expect(response.status).toBe(201);
      });

      // Performance should be reasonable (less than 5 seconds for 10 operations)
      expect(duration).toBeLessThan(5000);
    });

    it("should handle pagination efficiently", async () => {
      // Create multiple FAQ categories
      const categories = await Promise.all(
        Array.from({ length: 15 }, (_, i) =>
          testContext.context.db.models.FaqCategory.create({
            name: `Category ${i}`,
            description: `Description ${i}`,
            botId,
          })
        )
      );

      // Test pagination
      const page1Response = await request(testContext.app)
        .get("/faq-categories")
        .query({ page: 1, limit: 10 })
        .expect(200);

      const page2Response = await request(testContext.app)
        .get("/faq-categories")
        .query({ page: 2, limit: 10 })
        .expect(200);

      expect(page1Response.body.data.items).toHaveLength(10);
      expect(page2Response.body.data.items).toHaveLength(5);
      expect(page1Response.body.data.pagination.totalItems).toBe(15);
      expect(page1Response.body.data.pagination.totalPages).toBe(2);
    });
  });
});