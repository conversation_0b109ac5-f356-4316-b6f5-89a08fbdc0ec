# Integration Tests

This directory contains comprehensive integration tests for the bot-builder-service API endpoints.

## Overview

The integration tests simulate real-world request/response cycles, covering the full lifecycle from HTTP request to database persistence and response. They use an in-memory SQLite database to test actual persistence behavior without mocks or stubs.

## Test Structure

### Core Test Files

- **`test-setup.ts`** - Database and application setup utilities
- **`test-utilities.ts`** - Reusable test factories and assertion helpers
- **`index.test.ts`** - Main integration test suite covering cross-cutting concerns

### Endpoint-Specific Tests

- **`language.integration.test.ts`** - Language management endpoints
- **`bot-language.integration.test.ts`** - Bot-language assignment endpoints  
- **`faq.integration.test.ts`** - FAQ categories and translations
- **`intent.integration.test.ts`** - Intent items and utterance translations
- **`entity.integration.test.ts`** - Entity management endpoints
- **`training.integration.test.ts`** - Complete training data workflows

## Test Coverage

The tests cover all API endpoints except:
- Bot endpoints (`/bots/*`)
- Flow endpoints (`/flows/*`) 
- App endpoints (`/apps/*`)

### Covered Endpoints

#### Language Endpoints
- `POST /languages` - Create language
- `GET /languages` - List languages with pagination
- `GET /languages/:id` - Get language by ID

#### Bot Language Endpoints
- `POST /bot-languages` - Assign language to bot
- `GET /bot-languages` - List bot languages
- `GET /bot-languages/:id` - Get bot language by ID
- `DELETE /bot-languages/:id` - Unassign language from bot

#### FAQ Endpoints
- `POST /faq-categories` - Create FAQ category
- `GET /faq-categories` - List FAQ categories
- `GET /faq-categories/:id` - Get FAQ category by ID
- `PUT /faq-categories/:id` - Update FAQ category
- `DELETE /faq-categories/:id` - Delete FAQ category
- `POST /faq-translations` - Create FAQ translation
- `PUT /faq-translations/:id` - Update FAQ translation
- `GET /faqs` - Get FAQs by category and language
- `GET /faq/:faqId/translations` - Get all translations for FAQ
- `GET /faq/:faqId/lang/:langId/translation` - Get specific translation

#### Intent Endpoints
- `POST /intent-items` - Create intent item
- `GET /intent-items` - List intent items
- `GET /intent-items/:id` - Get intent item by ID
- `PUT /intent-items/:id` - Update intent item
- `DELETE /intent-items/:id` - Delete intent item
- `POST /intent-items/assign-flow` - Assign flow to intent
- `POST /intent/:intentId/lang/:langId/intent-utterance` - Create utterance translation
- `GET /intent/:intentId/lang/:langId/intent-utterance` - List utterances for intent/language
- `GET /intent-utterance/:id` - Get utterance translation by ID
- `GET /utterance/:utteranceId/lang/:langId/translation` - Get translation by utterance/language
- `PUT /intent-utterance/:id` - Update utterance translation
- `DELETE /intent-utterance/:id` - Delete utterance translation

#### Entity Endpoints
- `POST /entities` - Create entity
- `GET /entities` - List entities with filtering and pagination
- `GET /entities/:id` - Get entity by ID
- `PUT /entities/:id` - Update entity
- `DELETE /entities/:id` - Delete entity

## Test Features

### Database Testing
- Uses real in-memory SQLite database
- Tests actual persistence behavior
- Validates database constraints and relationships
- Tests cascade operations

### Request/Response Testing
- Full HTTP request/response cycle testing
- Validates request/response structure
- Tests error handling and status codes
- Validates API response consistency

### Business Logic Testing
- Tests complex workflows and relationships
- Validates business rules and constraints
- Tests multilingual data handling
- Tests concurrent operations

### Performance Testing
- Tests bulk operations
- Validates pagination efficiency
- Tests concurrent request handling
- Performance benchmarking

## Running Tests

```bash
# Run all tests
npm test

# Run integration tests only
npm test -- --testPathPattern=integration

# Run with coverage
npm run test:coverage

# Run specific test file
npm test -- language.integration.test.ts

# Run in watch mode
npm run test:watch
```

## Test Data Management

### Test Factories
The `TestDataFactory` class provides methods to create test data:

```typescript
const factory = new TestDataFactory(testContext);
const bot = await factory.createBot();
const language = await factory.createLanguage();
const trainingSet = await factory.createCompleteTrainingSet();
```

### Test Assertions
The `TestAssertions` class provides common assertion helpers:

```typescript
TestAssertions.expectValidApiResponse(response, 201);
TestAssertions.expectValidPaginatedResponse(response);
TestAssertions.expectValidErrorResponse(response, 400, 'VALIDATION_ERROR');
```

## Best Practices

### DRY Principles
- Reusable test setup and teardown
- Common test data factories
- Shared assertion helpers
- Modular test utilities

### Test Organization
- Clear test structure and naming
- Logical grouping of related tests
- Comprehensive test descriptions
- Consistent test patterns

### Maintainability
- Easy to extend for new endpoints
- Clear separation of concerns
- Minimal code duplication
- Well-documented test utilities

## Coverage Goals

The tests aim for 100% coverage of:
- All targeted API endpoints
- Request validation logic
- Response formatting
- Error handling paths
- Database operations
- Business logic flows

Current coverage thresholds:
- Branches: 90%
- Functions: 90%
- Lines: 90%
- Statements: 90%