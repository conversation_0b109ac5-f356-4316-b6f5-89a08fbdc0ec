import request from "supertest";
import { TestContext, createTestApp } from "./test-setup";
import { TestDataFactory, TestAssertions } from "./test-utilities";

describe("Integration Test Suite", () => {
  let testContext: TestContext;
  let testFactory: TestDataFactory;

  beforeAll(async () => {
    testContext = await createTestApp();
    testFactory = new TestDataFactory(testContext);
  });

  afterAll(async () => {
    await testContext.db.close();
  });

  beforeEach(async () => {
    await testContext.db.sync({ force: true });
  });

  describe("API Health and Structure", () => {
    it("should have all required endpoints accessible", async () => {
      const endpoints = [
        { method: 'GET', path: '/languages', expectedStatus: 200 },
        { method: 'GET', path: '/bot-languages', expectedStatus: 200 },
        { method: 'GET', path: '/faq-categories', expectedStatus: 200 },
        { method: 'GET', path: '/intent-items', expectedStatus: 200 },
        { method: 'GET', path: '/entities', expectedStatus: 200 },
      ];

      for (const endpoint of endpoints) {
        const response = await request(testContext.app)
          [endpoint.method.toLowerCase() as 'get'](endpoint.path);
        
        expect(response.status).toBe(endpoint.expectedStatus);
      }
    });

    it("should return consistent API response structure", async () => {
      const response = await request(testContext.app)
        .get('/languages')
        .expect(200);

      TestAssertions.expectValidPaginatedResponse(response);
    });

    it("should handle invalid routes gracefully", async () => {
      const response = await request(testContext.app)
        .get('/non-existent-endpoint')
        .expect(404);

      expect(response.status).toBe(404);
    });
  });

  describe("Cross-Entity Integration", () => {
    it("should maintain referential integrity across entities", async () => {
      const trainingSet = await testFactory.createCompleteTrainingSet();

      // Verify all relationships exist
      const botLanguage = await testContext.db.models.BotLanguage.findOne({
        where: { botId: trainingSet.bot.id, langId: trainingSet.language.id }
      });
      expect(botLanguage).not.toBeNull();

      const intentWithFlow = await testContext.db.models.IntentItems.findByPk(trainingSet.intentItem.id);
      expect(intentWithFlow?.flowId).toBe(trainingSet.flow.id);

      const faqInCategory = await testContext.db.models.FaqItems.findByPk(trainingSet.faqItem.id);
      expect(faqInCategory?.categoryId).toBe(trainingSet.faqCategory.id);
    });

    it("should handle cascade operations correctly", async () => {
      const bot = await testFactory.createBot();
      const language = await testFactory.createLanguage();
      const botLanguage = await testFactory.createBotLanguage(bot.id, language.id);
      const entity = await testFactory.createEntity(bot.id);

      // Delete bot should cascade to related entities
      await testContext.db.models.Bot.destroy({ where: { id: bot.id } });

      const deletedBotLanguage = await testContext.db.models.BotLanguage.findByPk(botLanguage.id);
      const deletedEntity = await testContext.db.models.Entities.findByPk(entity.id);

      expect(deletedBotLanguage).toBeNull();
      expect(deletedEntity).toBeNull();
    });
  });

  describe("Data Consistency and Validation", () => {
    it("should enforce unique constraints", async () => {
      const bot = await testFactory.createBot();
      
      // Create first entity
      await testFactory.createEntity(bot.id, { name: "unique_entity" });

      // Attempt to create duplicate should fail
      try {
        await testFactory.createEntity(bot.id, { name: "unique_entity" });
        fail("Should have thrown unique constraint error");
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    it("should validate required fields", async () => {
      const response = await request(testContext.app)
        .post('/entities')
        .send({})
        .expect(400);

      TestAssertions.expectValidErrorResponse(response, 400);
    });

    it("should handle concurrent operations safely", async () => {
      const bot = await testFactory.createBot();
      
      const promises = Array.from({ length: 5 }, (_, i) =>
        testFactory.createEntity(bot.id, { name: `concurrent_entity_${i}` })
      );

      const results = await Promise.all(promises);
      expect(results).toHaveLength(5);
      
      // Verify all entities were created
      const entities = await testContext.db.models.Entities.findAll({
        where: { botId: bot.id }
      });
      expect(entities).toHaveLength(5);
    });
  });

  describe("Performance and Scalability", () => {
    it("should handle large datasets efficiently", async () => {
      const bot = await testFactory.createBot();
      const language = await testFactory.createLanguage();

      const startTime = Date.now();

      // Create 50 entities
      const entityPromises = Array.from({ length: 50 }, (_, i) =>
        testFactory.createEntity(bot.id, { name: `perf_entity_${i}` })
      );

      await Promise.all(entityPromises);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (10 seconds)
      expect(duration).toBeLessThan(10000);

      // Verify pagination works with large dataset
      const response = await request(testContext.app)
        .get('/entities')
        .query({ page: 1, limit: 20 })
        .expect(200);

      expect(response.body.data.items).toHaveLength(20);
      expect(response.body.data.pagination.totalItems).toBe(50);
    });

    it("should optimize database queries", async () => {
      const trainingSet = await testFactory.createCompleteTrainingSet();

      // Test complex query with joins
      const response = await request(testContext.app)
        .get('/faqs')
        .query({ 
          categoryId: trainingSet.faqCategory.id, 
          langId: trainingSet.language.id 
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
    });
  });

  describe("Error Handling and Recovery", () => {
    it("should handle database connection issues gracefully", async () => {
      // Simulate database error by closing connection temporarily
      await testContext.db.close();

      const response = await request(testContext.app)
        .get('/languages')
        .expect(500);

      TestAssertions.expectValidErrorResponse(response, 500);

      // Reconnect for cleanup
      await testContext.db.connect();
    });

    it("should provide meaningful error messages", async () => {
      const response = await request(testContext.app)
        .get('/languages/invalid-uuid-format')
        .expect(404);

      TestAssertions.expectValidErrorResponse(response, 404);
      expect(response.body.error.message).toBeDefined();
    });

    it("should handle malformed request data", async () => {
      const response = await request(testContext.app)
        .post('/entities')
        .send({ invalidField: "invalid data" })
        .expect(400);

      TestAssertions.expectValidErrorResponse(response, 400);
    });
  });

  describe("Security and Authorization", () => {
    it("should require authentication for protected endpoints", async () => {
      // Mock auth middleware to simulate unauthorized request
      const originalAuth = require('../../middleware/auth.middleware');
      jest.doMock('../../middleware/auth.middleware', () => ({
        authMiddleware: (req: any, res: any, next: any) => {
          res.status(401).json({ success: false, error: { message: 'Unauthorized' } });
        }
      }));

      const response = await request(testContext.app)
        .post('/entities')
        .send({
          name: "test_entity",
          description: "Test entity",
          botId: "test-bot-id",
          entityType: "custom",
          values: ["test"],
        })
        .expect(401);

      TestAssertions.expectValidErrorResponse(response, 401);
    });

    it("should validate user permissions", async () => {
      const bot = await testFactory.createBot({ createdBy: "different-user" });
      
      // User should only access their own bot's data
      const response = await request(testContext.app)
        .post('/entities')
        .send({
          name: "unauthorized_entity",
          description: "Unauthorized entity",
          botId: bot.id,
          entityType: "custom",
          values: ["test"],
        });

      // This should either succeed (if no user-level filtering) or fail with 403
      expect([201, 403]).toContain(response.status);
    });
  });
});