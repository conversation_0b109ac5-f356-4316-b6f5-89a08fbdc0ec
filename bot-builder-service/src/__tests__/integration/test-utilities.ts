import { TestContext } from "./test-setup";
import { BotStatus } from "@neuratalk/bot-store";
import { v4 as uuidv4 } from "uuid";

const TEST_USER_ID = uuidv4();

export class TestDataFactory {
  constructor(private context: TestContext) {}

  async createBot(overrides: Partial<any> = {}) {
    return await this.context.db.models.Bot.create({
      name: "Test Bot",
      description: "Test bot description",
      status: BotStatus.DRAFT,
      createdBy: TEST_USER_ID,
      updatedBy: TEST_USER_ID,
      ...overrides,
    });
  }

  async createLanguage(overrides: Partial<any> = {}) {
    return await this.context.db.models.Language.create({
      name: "English",
      code: "en",
      ...overrides,
    });
  }

  async createFlow(botId: string, overrides: Partial<any> = {}) {
    return await this.context.db.models.Flow.create({
      name: "Test Flow",
      description: "Test flow description",
      botId,
      createdBy: TEST_USER_ID,
      updatedBy: TEST_USER_ID,
      ...overrides,
    });
  }

  async createFaqCategory(botId: string, overrides: Partial<any> = {}) {
    return await this.context.db.models.FaqCategory.create({
      name: "Test Category",
      description: "Test category description",
      botId,
      createdBy: TEST_USER_ID,
      updatedBy: TEST_USER_ID,
      ...overrides,
    });
  }

  async createFaqItem(botId: string, categoryId: string, overrides: Partial<any> = {}) {
    return await this.context.db.models.FaqItems.create({
      botId,
      categoryId,
      createdBy: TEST_USER_ID,
      updatedBy: TEST_USER_ID,
      ...overrides,
    });
  }

  async createFaqTranslation(faqId: string, langId: string, overrides: Partial<any> = {}) {
    return await this.context.db.models.FaqTranslation.create({
      faqId,
      langId,
      questions: ["Test question?"],
      answer: "Test answer",
      createdBy: TEST_USER_ID,
      updatedBy: TEST_USER_ID,
      ...overrides,
    });
  }

  async createIntentItem(botId: string, overrides: Partial<any> = {}) {
    return await this.context.db.models.IntentItems.create({
      name: "test_intent",
      description: "Test intent description",
      botId,
      createdBy: TEST_USER_ID,
      updatedBy: TEST_USER_ID,
      ...overrides,
    });
  }

  async createIntentUtterance(intentId: string, overrides: Partial<any> = {}) {
    return await this.context.db.models.IntentUtterance.create({
      intentId,
      createdBy: "test-user-id",
      ...overrides,
    });
  }

  async createUtteranceTranslation(
    utteranceId: string,
    langId: string,
    overrides: Partial<any> = {},
  ) {
    return await this.context.db.models.UtteranceTranslation.create({
      utteranceId,
      langId,
      text: "Test utterance text",
      createdBy: TEST_USER_ID,
      updatedBy: TEST_USER_ID,
      ...overrides,
    });
  }

  async createEntity(botId: string, intentId: string, overrides: Partial<any> = {}) {
    return await this.context.db.models.Entities.create({
      name: "test_entity",
      botId,
      intentId,
      createdBy: TEST_USER_ID,
      updatedBy: TEST_USER_ID,
      ...overrides,
    });
  }

  async createBotLanguage(botId: string, langId: string, overrides: Partial<any> = {}) {
    return await this.context.db.models.BotLanguage.create({
      botId,
      langId,
      isDefault: false,
      createdBy: "test-user-id",
      ...overrides,
    });
  }

  async createCompleteTrainingSet(botName: string = "Complete Training Bot") {
    // Create bot and language
    const bot = await this.createBot({ name: botName });
    const language = await this.createLanguage();
    const botLanguage = await this.createBotLanguage(bot.id, language.id, { isDefault: true });

    // Create flow
    const flow = await this.createFlow(bot.id);

    // Create FAQ data
    const faqCategory = await this.createFaqCategory(bot.id);
    const faqItem = await this.createFaqItem(bot.id, faqCategory.id);
    const faqTranslation = await this.createFaqTranslation(faqItem.id, language.id);

    // Create intent data
    const intentItem = await this.createIntentItem(bot.id, { flowId: flow.id });
    const intentUtterance = await this.createIntentUtterance(intentItem.id);
    const utteranceTranslation = await this.createUtteranceTranslation(
      intentUtterance.id,
      language.id,
    );

    // Create entity data
    const entity = await this.createEntity(bot.id,intentItem.id);

    return {
      bot,
      language,
      botLanguage,
      flow,
      faqCategory,
      faqItem,
      faqTranslation,
      intentItem,
      intentUtterance,
      utteranceTranslation,
      entity,
    };
  }
}

export class TestAssertions {
  static expectValidApiResponse(response: any, expectedStatus: number = 200) {
    expect(response.status).toBe(expectedStatus);
    expect(response.body).toHaveProperty("success");
    expect(response.body).toHaveProperty("timestamp");

    if (response.body.success) {
      expect(response.body).toHaveProperty("data");
    } else {
      expect(response.body).toHaveProperty("error");
    }
  }

  static expectValidPaginatedResponse(response: any) {
    this.expectValidApiResponse(response);
    expect(response.body.data).toHaveProperty("items");
    expect(response.body.data).toHaveProperty("pagination");
    expect(response.body.data.pagination).toHaveProperty("page");
    expect(response.body.data.pagination).toHaveProperty("limit");
    expect(response.body.data.pagination).toHaveProperty("totalItems");
    expect(response.body.data.pagination).toHaveProperty("totalPages");
  }

  static expectValidErrorResponse(response: any, expectedStatus: number, expectedCode?: string) {
    expect(response.status).toBe(expectedStatus);
    expect(response.body.success).toBe(false);
    expect(response.body).toHaveProperty("error");
    expect(response.body.error).toHaveProperty("message");

    if (expectedCode) {
      expect(response.body.error.code).toBe(expectedCode);
    }
  }

  static expectValidUuid(value: string) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    expect(value).toMatch(uuidRegex);
  }

  static expectValidTimestamp(value: string) {
    const date = new Date(value);
    expect(date).toBeInstanceOf(Date);
    expect(date.getTime()).not.toBeNaN();
  }
}

export const createMockRequest = (overrides: any = {}) => ({
  body: {},
  params: {},
  query: {},
  user: { id: "test-user-id", email: "<EMAIL>" },
  ...overrides,
});

export const createMockResponse = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.headersSent = false;
  return res;
};

export const waitForAsync = (ms: number = 100) => new Promise((resolve) => setTimeout(resolve, ms));
