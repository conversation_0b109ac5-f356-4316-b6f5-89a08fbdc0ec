import { DatabaseConnection } from "@neuratalk/bot-store";
import { AppContext } from "../../types/context.types";
import { App } from "../../app";
import { RequestHandler } from "express";

export interface TestContext {
  app: App;
  db: DatabaseConnection;
  context: AppContext;
}

export const setupTestDatabase = async (): Promise<DatabaseConnection> => {
  const db = new DatabaseConnection("test");

  await db.connect();
  await db.getSequelize().sync({ force: true });
  return db;
};

export const createTestApp = (): TestContext => {
  const app = new App();
  const context = app.getContext();
  const db = context.db;

  return { app, db, context };
};

export const createTestUser = () => ({
  id: "test-user-id",
  email: "<EMAIL>",
  name: "Test User",
});

export const mockAuthMiddleware: RequestHandler = (req, res, next) => {
  req.user = createTestUser();
  next();
};
