import request from "supertest";
import { TestContext, createTestApp } from "./test-setup";
import { createEntityRoutes } from "../../routers/entity.routes";
import { BotStatus } from "@neuratalk/bot-store";

describe("Entity Integration Tests", () => {
  let testContext: TestContext;
  let botId: string;
  let intentId: string;

  beforeAll(async () => {
    testContext = await createTestApp();
    testContext.app.use("/", createEntityRoutes(testContext.context));
  });

  afterAll(async () => {
    await testContext.db.disconnect();
  });

  beforeEach(async () => {
    await testContext.db.sequelize.sync({ force: true });
    
    const bot = await testContext.context.db.models.Bot.create({
      name: "Test Bot",
      description: "Test bot description",
      status: BotStatus.DRAFT,
      createdBy: "test-user-id",
      updatedBy: "test-user-id",
    });
    botId = bot.id;

    const intent = await testContext.context.db.models.IntentItems.create({
      name: "test_intent",
      description: "Test intent",
      botId,
      createdBy: "test-user-id",
      updatedBy: "test-user-id",
    });
    intentId = intent.id;
  });

  describe("POST /entities", () => {
    it("should create entity successfully", async () => {
      const entityData = {
        name: "person",
        botId,
        intentId,
      };

      const response = await request(testContext.app)
        .post("/entities")
        .send(entityData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        name: "person",
        botId,
        intentId,
      });
      expect(response.body.data.id).toBeDefined();
    });

    it("should return validation error for missing required fields", async () => {
      const response = await request(testContext.app)
        .post("/entities")
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe("GET /entities", () => {
    beforeEach(async () => {
      await testContext.context.db.models.Entities.bulkCreate([
        {
          name: "color",
          botId,
          intentId,
          createdBy: "test-user-id",
          updatedBy: "test-user-id",
        },
        {
          name: "number",
          botId,
          intentId,
          createdBy: "test-user-id",
          updatedBy: "test-user-id",
        },
      ]);
    });

    it("should retrieve all entities with pagination", async () => {
      const response = await request(testContext.app)
        .get("/entities")
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(2);
      expect(response.body.data.pagination).toBeDefined();
    });

    it("should filter entities by search", async () => {
      const response = await request(testContext.app)
        .get("/entities")
        .query({ search: "color" })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(1);
      expect(response.body.data.items[0].name).toBe("color");
    });
  });

  describe("GET /entities/:id", () => {
    let entityId: string;

    beforeEach(async () => {
      const entity = await testContext.context.db.models.Entities.create({
        name: "location",
        botId,
        intentId,
        createdBy: "test-user-id",
        updatedBy: "test-user-id",
      });
      entityId = entity.id;
    });

    it("should retrieve entity by ID", async () => {
      const response = await request(testContext.app)
        .get(`/entities/${entityId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe("location");
    });

    it("should return 404 for non-existent entity", async () => {
      const response = await request(testContext.app)
        .get("/entities/non-existent-id")
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe("Entity not found");
    });
  });

  describe("PUT /entities/:id", () => {
    let entityId: string;

    beforeEach(async () => {
      const entity = await testContext.context.db.models.Entities.create({
        name: "original_entity",
        botId,
        intentId,
        createdBy: "test-user-id",
        updatedBy: "test-user-id",
      });
      entityId = entity.id;
    });

    it("should update entity successfully", async () => {
      const updateData = {
        name: "updated_entity",
      };

      const response = await request(testContext.app)
        .put(`/entities/${entityId}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe("updated_entity");
    });

    it("should return 404 for non-existent entity", async () => {
      const updateData = {
        name: "non_existent",
      };

      const response = await request(testContext.app)
        .put("/entities/non-existent-id")
        .send(updateData)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe("DELETE /entities/:id", () => {
    let entityId: string;

    beforeEach(async () => {
      const entity = await testContext.context.db.models.Entities.create({
        name: "to_delete",
        botId,
        intentId,
        createdBy: "test-user-id",
        updatedBy: "test-user-id",
      });
      entityId = entity.id;
    });

    it("should delete entity successfully", async () => {
      await request(testContext.app)
        .delete(`/entities/${entityId}`)
        .expect(204);

      const deleted = await testContext.context.db.models.Entities.findByPk(entityId);
      expect(deleted).toBeNull();
    });

    it("should return 404 for non-existent entity", async () => {
      const response = await request(testContext.app)
        .delete("/entities/non-existent-id")
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe("Entity not found");
    });
  });
});