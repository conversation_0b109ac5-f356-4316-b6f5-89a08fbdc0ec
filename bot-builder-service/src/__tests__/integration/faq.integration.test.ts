import request from "supertest";
import { TestContext, createTestApp } from "./test-setup";
import { createFaqRoutes } from "../../routers/faq.routes";
import { BotStatus } from "@neuratalk/bot-store";

describe("FAQ Integration Tests", () => {
  let testContext: TestContext;
  let botId: string;
  let langId: string;

  beforeAll(async () => {
    testContext = await createTestApp();
    testContext.app.use("/", createFaqRoutes(testContext.context));
  });

  afterAll(async () => {
    await testContext.db.disconnect();
  });

  beforeEach(async () => {
    await testContext.db.sequelize.sync({ force: true });
    
    const bot = await testContext.context.db.models.Bot.create({
      name: "Test Bot",
      description: "Test bot description",
      status: BotStatus.DRAFT,
      createdBy: "test-user-id",
      updatedBy: "test-user-id",
    });
    botId = bot.id;

    const language = await testContext.context.db.models.Language.create({
      name: "English",
      code: "en",
    });
    langId = language.id;
  });

  describe("FAQ Categories", () => {
    describe("POST /faq-categories", () => {
      it("should create FAQ category successfully", async () => {
        const categoryData = {
          name: "General Questions",
          description: "General FAQ category",
          botId,
        };

        const response = await request(testContext.app)
          .post("/faq-categories")
          .send(categoryData)
          .expect(201);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toMatchObject({
          name: "General Questions",
          description: "General FAQ category",
          botId,
        });
      });

      it("should return validation error for missing required fields", async () => {
        const response = await request(testContext.app)
          .post("/faq-categories")
          .send({})
          .expect(400);

        expect(response.body.success).toBe(false);
      });
    });

    describe("GET /faq-categories", () => {
      beforeEach(async () => {
        await testContext.context.db.models.FaqCategory.bulkCreate([
          { name: "Category 1", description: "First category", botId, createdBy: "test-user-id", updatedBy: "test-user-id" },
          { name: "Category 2", description: "Second category", botId, createdBy: "test-user-id", updatedBy: "test-user-id" },
        ]);
      });

      it("should retrieve all FAQ categories", async () => {
        const response = await request(testContext.app)
          .get("/faq-categories")
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.items).toHaveLength(2);
      });
    });

    describe("GET /faq-categories/:id", () => {
      let categoryId: string;

      beforeEach(async () => {
        const category = await testContext.context.db.models.FaqCategory.create({
          name: "Test Category",
          description: "Test description",
          botId,
          createdBy: "test-user-id",
          updatedBy: "test-user-id",
        });
        categoryId = category.id;
      });

      it("should retrieve FAQ category by ID", async () => {
        const response = await request(testContext.app)
          .get(`/faq-categories/${categoryId}`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.name).toBe("Test Category");
      });
    });

    describe("PUT /faq-categories/:id", () => {
      let categoryId: string;

      beforeEach(async () => {
        const category = await testContext.context.db.models.FaqCategory.create({
          name: "Original Name",
          description: "Original description",
          botId,
          createdBy: "test-user-id",
          updatedBy: "test-user-id",
        });
        categoryId = category.id;
      });

      it("should update FAQ category successfully", async () => {
        const updateData = {
          name: "Updated Name",
          description: "Updated description",
        };

        const response = await request(testContext.app)
          .put(`/faq-categories/${categoryId}`)
          .send(updateData)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.name).toBe("Updated Name");
      });
    });

    describe("DELETE /faq-categories/:id", () => {
      let categoryId: string;

      beforeEach(async () => {
        const category = await testContext.context.db.models.FaqCategory.create({
          name: "To Delete",
          description: "Category to delete",
          botId,
          createdBy: "test-user-id",
          updatedBy: "test-user-id",
        });
        categoryId = category.id;
      });

      it("should delete FAQ category successfully", async () => {
        await request(testContext.app)
          .delete(`/faq-categories/${categoryId}`)
          .expect(204);

        const deleted = await testContext.context.db.models.FaqCategory.findByPk(categoryId);
        expect(deleted).toBeNull();
      });
    });
  });

  describe("FAQ Translations", () => {
    let categoryId: string;
    let faqItemId: string;

    beforeEach(async () => {
      const category = await testContext.context.db.models.FaqCategory.create({
        name: "Test Category",
        description: "Test description",
        botId,
        createdBy: "test-user-id",
        updatedBy: "test-user-id",
      });
      categoryId = category.id;

      const faqItem = await testContext.context.db.models.FaqItems.create({
        botId,
        categoryId,
        createdBy: "test-user-id",
        updatedBy: "test-user-id",
      });
      faqItemId = faqItem.id;
    });

    describe("POST /faq-translations", () => {
      it("should create FAQ translation successfully", async () => {
        const translationData = {
          faqId: faqItemId,
          langId,
          questions: ["What is this?"],
          answer: "This is a test FAQ",
        };

        const response = await request(testContext.app)
          .post("/faq-translations")
          .send(translationData)
          .expect(201);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toMatchObject({
          faqId: faqItemId,
          langId,
          questions: ["What is this?"],
          answer: "This is a test FAQ",
        });
      });
    });

    describe("GET /faqs", () => {
      beforeEach(async () => {
        await testContext.context.db.models.FaqTranslation.create({
          faqId: faqItemId,
          langId,
          questions: ["Test Question"],
          answer: "Test Answer",
          createdBy: "test-user-id",
          updatedBy: "test-user-id",
        });
      });

      it("should retrieve FAQs by category and language", async () => {
        const response = await request(testContext.app)
          .get("/faqs")
          .query({ categoryId, langId })
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].questions).toEqual(["Test Question"]);
      });
    });

    describe("GET /faq/:faqId/translations", () => {
      beforeEach(async () => {
        await testContext.context.db.models.FaqTranslation.create({
          faqId: faqItemId,
          langId,
          questions: ["Test Question"],
          answer: "Test Answer",
          createdBy: "test-user-id",
          updatedBy: "test-user-id",
        });
      });

      it("should retrieve all translations for a FAQ", async () => {
        const response = await request(testContext.app)
          .get(`/faq/${faqItemId}/translations`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toHaveLength(1);
      });
    });

    describe("GET /faq/:faqId/lang/:langId/translation", () => {
      beforeEach(async () => {
        await testContext.context.db.models.FaqTranslation.create({
          faqId: faqItemId,
          langId,
          questions: ["Specific Question"],
          answer: "Specific Answer",
          createdBy: "test-user-id",
          updatedBy: "test-user-id",
        });
      });

      it("should retrieve specific translation by FAQ and language", async () => {
        const response = await request(testContext.app)
          .get(`/faq/${faqItemId}/lang/${langId}/translation`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.questions).toEqual(["Specific Question"]);
      });
    });

    describe("PUT /faq-translations/:id", () => {
      let translationId: string;

      beforeEach(async () => {
        const translation = await testContext.context.db.models.FaqTranslation.create({
          faqId: faqItemId,
          langId,
          questions: ["Original Question"],
          answer: "Original Answer",
          createdBy: "test-user-id",
          updatedBy: "test-user-id",
        });
        translationId = translation.id;
      });

      it("should update FAQ translation successfully", async () => {
        const updateData = {
          questions: ["Updated Question"],
          answer: "Updated Answer",
        };

        const response = await request(testContext.app)
          .put(`/faq-translations/${translationId}`)
          .send(updateData)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.questions).toEqual(["Updated Question"]);
      });
    });
  });
});