/* eslint-disable @typescript-eslint/no-namespace */
import { Request, Response, NextFunction } from "express";
import { ApiResponse } from "@neuratalk/common";
import { logger } from "@neuratalk/common";
import { ADMIN_USER_ID } from "../config";

declare global {
  namespace Express {
    interface Request {
      user: {
        id: string;
        [key: string]: any;
      };
    }
  }
}

export function authMiddleware(req: Request, res: Response, next: NextFunction): void {
  try {
    const userId = (req.headers["x-user-id"] as string) || ADMIN_USER_ID;

    req.user = {
      id: userId,
    };

    next();
  } catch (error) {
    logger.error("Error in auth middleware:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "AUTH_ERROR",
        message: "Authentication failed",
      },
      timestamp: new Date(),
    } as ApiResponse);
  }
}
