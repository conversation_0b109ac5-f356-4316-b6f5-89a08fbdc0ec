/**
 * Bot Configuration and Management Types
 *
 * These types define bot entities, settings, and configuration options.
 */

// Forward declaration to avoid circular imports
export interface CreateFlowRequest {
  name: string;
  description?: string;
  botId: string;
  nodes: Record<string, any>;
  entryNodeId: string;
  metadata?: Record<string, any>;
  type?: string;
}

// --- Bot Entity ---

export interface BotSettings {
  // NLU Configuration
  nlu: {
    provider: "rasa" | "dialogflow" | "luis" | "custom";
    endpoint?: string;
    apiKey?: string;
    modelName?: string;
    confidenceThreshold: number; // Minimum confidence for intent matching
    fallbackIntent: string; // Intent to use when confidence is too low
  };

  // Session Management
  session: {
    ttlMinutes: number; // Session timeout in minutes
    maxConcurrentSessions: number; // Per user limit
    persistContext: boolean; // Whether to persist context to database
    enableSessionResumption: boolean; // Allow resuming expired sessions
  };

  // Message Handling
  messaging: {
    enableTypingIndicator: boolean;
    typingDelayMs: number; // Delay before showing typing indicator
    maxMessageLength: number; // Maximum message length
    enableQuickReplies: boolean;
    enableRichMessages: boolean; // Cards, carousels, etc.
    defaultErrorMessage: string;
    defaultFallbackMessage: string;
  };

  // Flow Execution
  execution: {
    maxExecutionTimeMs: number; // Maximum time for flow execution
    maxLoopIterations: number; // Prevent infinite loops
    enableAsyncOperations: boolean;
    asyncTimeoutMs: number; // Timeout for async operations
    enableScriptExecution: boolean;
    scriptTimeoutMs: number; // Timeout for script nodes
  };

  // Integration Settings
  integrations: {
    webhook?: {
      url: string;
      secret?: string;
      events: string[]; // Which events to send
    };
    analytics?: {
      enabled: boolean;
      provider?: "google" | "mixpanel" | "custom";
      trackingId?: string;
    };
    logging?: {
      level: "debug" | "info" | "warn" | "error";
      enableChatHistory: boolean;
      retentionDays: number;
    };
  };

  // Security Settings
  security: {
    enableRateLimit: boolean;
    rateLimitPerMinute: number;
    enableInputValidation: boolean;
    allowedFileTypes: string[];
    maxFileSize: number; // In bytes
    enableContentFilter: boolean;
  };
}

// --- Bot Templates ---

export interface BotTemplate {
  id: string;
  name: string;
  description: string;
  category: "customer_service" | "ecommerce" | "lead_generation" | "faq" | "booking" | "custom";
  settings: Partial<BotSettings>;
  flows: CreateFlowRequest[];
  metadata?: Record<string, any>;
}

// --- Bot Deployment ---

export interface BotDeployment {
  id: string;
  botId: string;
  version: string;
  environment: "development" | "staging" | "production";
  status: "pending" | "deploying" | "deployed" | "failed" | "rolled_back";
  deployedAt?: Date;
  deployedBy?: string;
  rollbackReason?: string;
  healthCheck?: {
    status: "healthy" | "unhealthy";
    lastChecked: Date;
    issues?: string[];
  };
}

// --- Bot Statistics ---

export interface BotStatistics {
  botId: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    totalConversations: number;
    activeUsers: number;
    messagesExchanged: number;
    averageSessionDuration: number;
    completionRate: number;
    fallbackRate: number;
    errorRate: number;
  };
  topIntents: Array<{
    intent: string;
    count: number;
    successRate: number;
  }>;
  topFlows: Array<{
    flowId: string;
    flowName: string;
    executions: number;
    completionRate: number;
    averageDuration: number;
  }>;
  performance: {
    averageResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    errorCount: number;
    timeoutCount: number;
  };
}

// --- Bot Validation ---

export interface BotValidationResult {
  isValid: boolean;
  errors: BotValidationError[];
  warnings: BotValidationWarning[];
}

export interface BotValidationError {
  type:
    | "missing_entry_flow"
    | "orphaned_node"
    | "invalid_condition"
    | "circular_reference"
    | "missing_intent";
  message: string;
  flowId?: string;
  nodeId?: string;
  details?: Record<string, any>;
}

export interface BotValidationWarning {
  type: "unreachable_node" | "missing_fallback" | "low_confidence_threshold" | "deprecated_feature";
  message: string;
  flowId?: string;
  nodeId?: string;
  suggestion?: string;
}

/**
 * Build bot response
 */
export interface BuildBotResponse {
  botId: string;
  buildPath: string;
  status: "success" | "failed";
  timestamp: Date;
  error?: string;
}
