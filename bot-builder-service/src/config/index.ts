/**
 * Configuration Management
 *
 * Centralized configuration for the bot-builder-service.
 */

import dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";

// Load environment variables
dotenv.config();

export interface Config {
  server: {
    port: number;
    env: string;
    corsOrigins: string[];
  };
  database: {
    url: string;
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
    ssl: boolean;
    maxConnections: number;
  };
  security: {
    jwtSecret: string;
    bcryptRounds: number;
  };
  services: {
    botInteractionUrl: string;
    chatServiceUrl: string;
  };
  logging: {
    level: string;
    file?: string;
  };
}

const config: Config = {
  server: {
    port: parseInt(process.env.PORT || "3000", 10),
    env: process.env.NODE_ENV || "development",
    corsOrigins: process.env.CORS_ORIGINS?.split(",") || ["http://localhost:3000"],
  },
  database: {
    url: process.env.DATABASE_URL || "postgresql://username:password@localhost:5432/chatbot_db",
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT || "5432", 10),
    name: process.env.DB_NAME || "chatbot_db",
    user: process.env.DB_USER || "username",
    password: process.env.DB_PASSWORD || "",
    ssl: process.env.DB_SSL === "true",
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || "20", 10),
  },
  security: {
    jwtSecret: process.env.JWT_SECRET || "your-super-secret-jwt-key",
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || "12", 10),
  },
  services: {
    botInteractionUrl: process.env.BOT_INTERACTION_SERVICE_URL || "http://localhost:3001",
    chatServiceUrl: process.env.CHAT_SERVICE_URL || "http://localhost:3002",
  },
  logging: {
    level: process.env.LOG_LEVEL || "info",
    file: process.env.LOG_FILE,
  },
};

export default config;

// TODO: Remove ADMIN_USER_ID fallback after implementing proper user authentication.
export const ADMIN_USER_ID = process.env.ADMIN_USER_ID || uuidv4();
