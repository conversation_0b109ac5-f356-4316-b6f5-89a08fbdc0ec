/**
 * Rasa Service
 *
 * Handles Rasa bot generation and related operations.
 */

import fs from "fs";
import path from "path";
import { promisify } from "util";
import { logger } from "@neuratalk/common";
import { BuildBotResponse } from "../types";
import { Bot, Models } from "@neuratalk/bot-store";

export class RasaService {
  private models: Models;

  constructor(models: Models) {
    this.models = models;
  }

  /**
   * Build a Rasa bot for the specified bot ID
   * Creates necessary files in the rasa-nlu directory
   */
  async buildBot(bot: Bot): Promise<BuildBotResponse> {
    throw new Error("RasaService.buildBot is currently disabled.");
  }
}
