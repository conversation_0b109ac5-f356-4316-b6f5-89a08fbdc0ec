/**
 * Flow Service
 *
 * Handles CRUD operations for flows and flow management.
 */

import { Op } from "sequelize";
import { CreateFlowRequest } from "../types";
import { v4 as uuidv4 } from "uuid";
import { logger } from "@neuratalk/common";
import { FlowAttributes as Flow, Models } from "@neuratalk/bot-store";

type UpdateFlowRequest = any; //TODO: need to add proper type

export class FlowService {
  private models: Models;

  constructor(models: Models) {
    this.models = models;
  }

  async createFlow(
    request: CreateFlowRequest,
    botId: string,
    appId: string,
    userId: string,
  ): Promise<Flow> {
    try {
      const newFlow = await this.models.Flow.create({
        id: uuidv4(),
        name: request.name,
        description: request.description,
        botId,
        appId,
        metadata: request.metadata || {},
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`Flow created: ${newFlow.id} - ${newFlow.name}`);
      return newFlow.toJSON();
    } catch (error) {
      logger.error("Error creating flow:", error);
      throw error;
    }
  }

  async getFlowById(id: string): Promise<Flow | null> {
    try {
      const flow = await this.models.Flow.findByPk(id);
      return flow ? flow.toJSON() : null;
    } catch (error) {
      logger.error(`Error getting flow ${id}:`, error);
      throw error;
    }
  }

  async updateFlow(id: string, request: UpdateFlowRequest, userId?: string): Promise<Flow | null> {
    try {
      const flow = await this.models.Flow.findByPk(id);
      if (!flow) {
        return null;
      }

      const updateData: Partial<UpdateFlowRequest & { updatedBy?: string }> = {
        ...request,
      };
      if (userId) {
        updateData.updatedBy = userId;
      }

      await flow.update(updateData);

      logger.info(`Flow updated: ${flow.id} - ${flow.name}`);
      return flow.toJSON();
    } catch (error) {
      logger.error(`Error updating flow ${id}:`, error);
      throw error;
    }
  }

  async deleteFlow(id: string): Promise<boolean> {
    try {
      const result = await this.models.Flow.destroy({ where: { id } });
      const deleted = result > 0;

      if (deleted) {
        logger.info(`Flow deleted: ${id}`);
      }

      return deleted;
    } catch (error) {
      logger.error(`Error deleting flow ${id}:`, error);
      throw error;
    }
  }

  async getFlowsByBotId(botId: string): Promise<any[]> {
    try {
      const flows = await this.models.Flow.findAll({ where: { botId } });
      return flows.map((flow) => {
        const flowJson = flow.toJSON();
        return {
          ...flowJson,
        };
      });
    } catch (error) {
      logger.error(`Error getting all flows for bot ${botId}:`, error);
      throw error;
    }
  }

  async getFlowsByBot(
    botId: string,
    page: number = 1,
    limit: number = 20,
    search?: string,
    isActive?: boolean,
  ): Promise<{ flows: Flow[]; total: number }> {
    try {
      const offset = (page - 1) * limit;
      const where: any = { botId };

      if (search) {
        where[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } },
        ];
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      const { count, rows } = await this.models.Flow.findAndCountAll({
        where,
        limit,
        offset,
        order: [["createdAt", "DESC"]],
      });

      return { flows: rows.map((r) => r.toJSON()), total: count };
    } catch (error) {
      logger.error(`Error getting flows for bot ${botId}:`, error);
      throw error;
    }
  }

  // async bulkCreateFlows(
  //   requests: CreateFlowRequest[],
  //   userId?: string,
  // ): Promise<{
  //   created: Flow[];
  //   failed: Array<{ request: CreateFlowRequest; error: string }>;
  // }> {
  //   const created: Flow[] = [];
  //   const failed: Array<{ request: CreateFlowRequest; error: string }> = [];

  //   for (const request of requests) {
  //     try {
  //       const flow = await this.createFlow(request, userId);
  //       created.push(flow);
  //     } catch (error) {
  //       failed.push({
  //         request,
  //         error: error instanceof Error ? error.message : "Unknown error",
  //       });
  //     }
  //   }

  //   logger.info(`Bulk flow creation: ${created.length} created, ${failed.length} failed`);

  //   return { created, failed };
  // }
}
