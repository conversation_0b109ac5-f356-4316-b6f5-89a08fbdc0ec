import { Router } from "express";
import { getStudioAppsService } from "api_gw";
import { validateBody, validateParams, validateQuery } from "@neuratalk/common";
import { AppController } from "../controllers/app.controller";
import { CreateAppSchema, UpdateAppSchema, AppIdParamSchema, GetAppsQuerySchema } from "../schemas";

export function createAppRoutes(): Router {
  const studioAppsService = getStudioAppsService();
  const router = Router();

  const appController = new AppController();

  const { check4Blacklist, validateCreateAppInfo, checkForDuplicateName } = studioAppsService;

  router.post(
    "/",
    check4Blacklist,
    validateBody(CreateAppSchema),
    validateCreateAppInfo,
    checkForDuplicateName,
    appController.createApp,
  );

  //TODO: not sure why we have it as we never gonna need a listing api's for apps, need to discuss
  // router.get("/", validateQuery(GetAppsQuerySchema), appController.getApps);

  router.put(
    "/:appId",
    validateParams(AppIdParamSchema),
    validateBody(UpdateAppSchema),
    appController.updateApp,
  );

  router.get("/:appId", validateParams(AppIdParamSchema), appController.getAppById);

  const appRouter = Router();
  appRouter.use("/apps", router);
  return appRouter;
}
