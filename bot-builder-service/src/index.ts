/**
 * Bot Builder Service Entry Point
 */
import dotenv from "dotenv";
dotenv.config();
import { initializeApiGw } from "api_gw";
import { App } from "./app";
import { logger } from "@neuratalk/common";

const main = async () => {
  try {
    await initializeApiGw();
    const app = new App();

    // Graceful shutdown handling
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      try {
        await app.stop();
        process.exit(0);
      } catch (error) {
        logger.error("Error during graceful shutdown:", error);
        process.exit(1);
      }
    };

    // Register shutdown handlers
    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    // Start the application
    app.start().catch((error) => {
      logger.error("Failed to start application:", error);
      process.exit(1);
    });
  } catch (err) {
    console.log("applicationDError", err);
  }
};

main();
