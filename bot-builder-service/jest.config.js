module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  roots: ["<rootDir>/src"],
  testMatch: ["**/__tests__/**/*.test.ts"],
  transform: {
    "^.+\\.ts$": "ts-jest",
  },
  collectCoverageFrom: [
    "src/**/*.ts", 
    "!src/**/*.d.ts", 
    "!src/index.ts", 
    "!src/__tests__/**",
    "!src/config/**",
    "!src/types/**"
  ],
  coverageDirectory: "coverage",
  coverageReporters: ["text", "lcov", "html"],
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },
  moduleNameMapping: {
    "^@neuratalk/(.*)$": "<rootDir>/../packages/$1/src",
  },
  setupFilesAfterEnv: ["<rootDir>/src/__tests__/integration/test-setup.ts"],
  testTimeout: 30000,
  maxWorkers: 1,
};
