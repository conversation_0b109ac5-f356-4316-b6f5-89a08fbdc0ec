# Server Configuration
PORT=3000
NODE_ENV=development
CORS_ORIGINS=http://localhost:3000

# Database Configuration
DATABASE_URL=postgresql://postgres:@localhost:5432/chatbot_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=chatbot_db
DB_USER=username
DB_PASSWORD=
DB_SSL=false
DB_MAX_CONNECTIONS=20

# Security
JWT_SECRET=your-super-secret-jwt-key
BCRYPT_ROUNDS=12
ADMIN_USER_ID=

# External Services
BOT_INTERACTION_SERVICE_URL=http://localhost:3001
CHAT_SERVICE_URL=http://localhost:3002

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
