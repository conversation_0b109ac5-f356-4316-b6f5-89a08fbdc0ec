# Dependencies
/node_modules

package-lock.json

# Production
/dist
/build


# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
wm.py

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDEs and editors
.idea
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Misc
coverage
npm-shrinkwrap.json
yarn.lock

app_store