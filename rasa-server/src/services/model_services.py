import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
from pathlib import Path
import tarfile
import requests
import tempfile
import shutil

from rasa.core.agent import Agent
from fastapi import HTTPException

from config.config import config

# Configure logging
logger = logging.getLogger(__name__)

class LoadedModel:
    def __init__(self, bot_id: str, agent: Agent, model_path: str):
        self.bot_id = bot_id
        self.agent = agent
        self.model_path = model_path
        self.loaded_at = datetime.now()
        self.last_used = datetime.now()

class ModelService:
    def __init__(self):
        self.loaded_models: Dict[str, LoadedModel] = {}
        self.cleanup_task = None
        self.ensure_directories()
    
    def _ensure_cleanup_task(self):
        """Ensure cleanup task is running"""
        if self.cleanup_task is None or self.cleanup_task.done():
            try:
                self.cleanup_task = asyncio.create_task(self.cleanup_scheduler())
            except RuntimeError:
                # No event loop running, will be created later
                pass

    def ensure_directories(self):
        """Ensure required directories exist"""
        Path(config.MODEL_PATH).mkdir(parents=True, exist_ok=True)
        Path(config.TEMP_PATH).mkdir(parents=True, exist_ok=True)

    async def load_model(self, bot_id: str, model_url: str) -> bool:
        """Load a Rasa model for a specific bot"""
        self._ensure_cleanup_task()
        try:
            if bot_id in self.loaded_models:
                self.loaded_models[bot_id].last_used = datetime.now()
                logger.info(f"Model for bot {bot_id} already loaded")
                return True

            model_path = await self.download_model(bot_id, model_url)
            logger.info(f"Loading model from path: {model_path}")

            model_path_abs = os.path.abspath(model_path)

            agent = Agent.load(model_path=model_path_abs)

            logger.info(f"Model for bot {bot_id} isReady: {agent.is_ready()}")
            self.loaded_models[bot_id] = LoadedModel(bot_id, agent, model_path)
            logger.info(f"Model for bot {bot_id} loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model for bot {bot_id}: {e}")
            raise

    async def download_model(self, bot_id: str, model_url: str) -> str:
        """Download or access a Rasa model from a URL or local path (if ENV=local)"""
        try:
            model_dir = Path(config.MODEL_PATH) / bot_id
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # Handle local paths only if ENV is 'local'
            if config.ENV.lower() == 'local':
                local_path = model_url[7:] if model_url.startswith('file://') else model_url
                model_path = Path(local_path).resolve()
                
                if not model_path.exists():
                    raise FileNotFoundError(f"Local model path does not exist: {model_path}")

                logger.info(f"Using local model for bot {bot_id} from {model_path}")
                
                return str(model_path)
            
            if not model_url.lower().startswith(('http://', 'https://')):
                raise ValueError(f"Invalid URL for remote model: {model_url}")
            
            logger.info(f"Downloading model for bot {bot_id} from {model_url}")
            model_tar_path = model_dir / "model.tar.gz"
            with requests.get(model_url, stream=True, timeout=300) as response:
                response.raise_for_status()
                with open(model_tar_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

            logger.info(f"Model for bot {bot_id} downloaded to {model_tar_path}")
            return str(model_tar_path)
            
        except Exception as e:
            logger.error(f"Error accessing model for bot {bot_id}: {str(e)}")
            raise

    async def process_message(self, bot_id: str, message: str, conversation_id: str) -> Dict[str, Any]:
        """Process a message using the loaded model"""
        if bot_id not in self.loaded_models:
            raise HTTPException(status_code=404, detail=f"Model for bot {bot_id} not loaded")
        
        try:
            model = self.loaded_models[bot_id]
            model.last_used = datetime.now()
            print('result, model.agent',model.agent)
            result = await model.agent.parse_message(message)
            print('result',result)
            
            return {
                'intent': result.get('intent', {}),
                'entities': result.get('entities', []),
                'text': message,
                'response': result.get('responses', [])
            }
        # TODO: need to remove this exception handling            
        # except AgentNotReady:
        #     logger.warning(f"Agent for bot {bot_id} is not ready yet")
        #     raise HTTPException(
        #         status_code=503,  # Service Unavailable
        #         detail=f"Agent for bot {bot_id} is not ready yet. Please try again later."
            # )
        
        except Exception as e:
            logger.error(f"Unexpected error processing message for bot {bot_id}: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Internal server error")

    async def unload_model(self, bot_id: str) -> bool:
        """Unload a model and clean up resources"""
        if bot_id not in self.loaded_models:
            logger.warning(f"Model for bot {bot_id} not found for unloading")
            return False
        
        try:
            model = self.loaded_models[bot_id]
            if os.path.exists(model.model_path):
                shutil.rmtree(os.path.dirname(model.model_path), ignore_errors=True)
            del self.loaded_models[bot_id]
            logger.info(f"Model for bot {bot_id} unloaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error unloading model for bot {bot_id}: {e}")
            return False

    def get_loaded_models(self) -> list:
        """Get list of loaded models"""
        return [
            {
                'botId': model.bot_id,
                'loadedAt': model.loaded_at.isoformat(),
                'lastUsed': model.last_used.isoformat()
            }
            for model in self.loaded_models.values()
        ]

    async def cleanup_scheduler(self):
        """Periodic cleanup of unused models"""
        while True:
            await asyncio.sleep(60)
            await self.cleanup_unused_models()

    async def cleanup_unused_models(self):
        """Clean up unused models based on TTL and max limit"""
        now = datetime.now()
        models_to_unload = []
        
        for bot_id, model in self.loaded_models.items():
            if (now - model.last_used).total_seconds() > config.MODEL_TTL:
                models_to_unload.append(bot_id)
        
        if len(self.loaded_models) > config.MAX_MODELS:
            sorted_models = sorted(
                self.loaded_models.items(),
                key=lambda x: x[1].last_used
            )
            excess_count = len(self.loaded_models) - config.MAX_MODELS
            models_to_unload.extend([bot_id for bot_id, _ in sorted_models[:excess_count]])
        
        for bot_id in set(models_to_unload):
            await self.unload_model(bot_id)
        
        if models_to_unload:
            logger.info(f"Cleaned up {len(set(models_to_unload))} unused models")