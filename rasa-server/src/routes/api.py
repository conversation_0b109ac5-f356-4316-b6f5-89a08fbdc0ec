import logging
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from datetime import datetime

from config.config import config
from models.request_models import ProcessMessageRequest, LoadModelRequest
from services.model_services import ModelService

logger = logging.getLogger(__name__)

# Initialize services
model_service = ModelService()
security = HTTPBearer()

async def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    Verify the API key provided in the Authorization header.

    Parameters:
        credentials (HTTPAuthorizationCredentials): Bearer token from the Authorization header.

    Returns:
        HTTPAuthorizationCredentials: The verified credentials.

    Raises:
        HTTPException: 401 if the API key is invalid.
    """
    if credentials.credentials != config.API_KEY:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return credentials

# FastAPI app
app = FastAPI(title="Rasa Server Service", version="1.0.0")

@app.get("/health")
async def health_check():
    """
    Check the health status of the Rasa Server Service.

    This endpoint verifies that the server is running and provides information about loaded models.

    Returns:
        dict: A JSON response with the following structure:
            - success (bool): Indicates if the request was successful.
            - data (dict): Contains health status details:
                - status (str): Health status ("healthy").
                - service (str): Service name ("rasa-server").
                - version (str): Service version ("1.0.0").
                - loadedModels (int): Number of currently loaded models.
                - timestamp (str): ISO timestamp of the response.
            - timestamp (str): ISO timestamp of the response (redundant for compatibility).

    Example Response:
        ```json
        {
            "success": true,
            "data": {
                "status": "healthy",
                "service": "rasa-server",
                "version": "1.0.0",
                "loadedModels": 0,
                "timestamp": "2025-07-11T17:34:00.123456"
            },
            "timestamp": "2025-07-11T17:34:00.123456"
        }
        ```
    """
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "service": "rasa-server",
            "version": "1.0.0",
            "loadedModels": len(model_service.loaded_models),
            "timestamp": datetime.now().isoformat()
        },
        "timestamp": datetime.now().isoformat()
    }

@app.post("/api/v1/process")
async def process_message(
    request: ProcessMessageRequest,
    credentials: HTTPAuthorizationCredentials = Depends(verify_api_key)
):
    """
    Process a user message using a loaded Rasa model.

    This endpoint processes a user message for a specific bot and conversation, optionally loading a model if provided.

    Parameters:
        request (ProcessMessageRequest): The request body containing:
            - botId (str): Unique identifier for the bot.
            - message (str): The user message to process.
            - conversationId (str): Unique identifier for the conversation.
            - modelUrl (str, optional): URL or local path (if ENV=local) to the Rasa model.
        credentials (HTTPAuthorizationCredentials): Bearer token for authentication.

    Returns:
        dict: A JSON response with the following structure:
            - success (bool): Indicates if the request was successful.
            - data (dict): Contains processing results:
                - botId (str): The bot identifier.
                - conversationId (str): The conversation identifier.
                - response (dict): Rasa processing results, including:
                    - intent (dict): Detected intent (or empty if none).
                    - entities (list): Detected entities (or empty if none).
                    - text (str): Original message.
                    - response (list): Rasa responses (or empty if none).
            - timestamp (str): ISO timestamp of the response.

    Raises:
        HTTPException: 
            - 401 if the API key is invalid.
            - 404 if the model for the bot is not loaded.
            - 500 if an error occurs during model loading or message processing.

    Example Request:
        ```json
        {
            "botId": "test-bot",
            "message": "Hello",
            "conversationId": "123",
            "modelUrl": "/path/to/model.tar.gz"
        }
        ```

    Example Response:
        ```json
        {
            "success": true,
            "data": {
                "botId": "test-bot",
                "conversationId": "123",
                "response": {
                    "intent": {"name": "greet", "confidence": 0.95},
                    "entities": [],
                    "text": "Hello",
                    "response": ["Hi there!"]
                }
            },
            "timestamp": "2025-07-11T17:34:00.123456"
        }
        ```
    """
    try:
        # TODO: remove conversationId from request payload
        if request.modelUrl and request.botId not in model_service.loaded_models:
            await model_service.load_model(request.botId, request.modelUrl)

        result = await model_service.process_message(
            request.botId, 
            request.message, 
            request.conversationId
        )
        
        return {
            "success": True,
            "data": {
                "botId": request.botId,
                "conversationId": request.conversationId,
                "response": result
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException as http_exc:
        logger.warning(f"HTTP error processing message: {http_exc.detail}")
        raise http_exc

    except Exception as e:
        logger.error(f"Unexpected error processing message: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/api/v1/models/load")
async def load_model(
    request: LoadModelRequest,
    credentials: HTTPAuthorizationCredentials = Depends(verify_api_key)
):
    """
    Load a Rasa model for a specific bot.

    This endpoint downloads (or accesses locally if ENV=local) and loads a Rasa model for the specified bot.

    Parameters:
        request (LoadModelRequest): The request body containing:
            - botId (str): Unique identifier for the bot.
            - modelUrl (str): URL to the model (HTTP/HTTPS) or local path (if ENV=local).
        credentials (HTTPAuthorizationCredentials): Bearer token for authentication.

    Returns:
        dict: A JSON response with the following structure:
            - success (bool): Indicates if the request was successful.
            - data (dict): Contains loading results:
                - botId (str): The bot identifier.
                - loaded (bool): Indicates if the model was loaded successfully.
            - timestamp (str): ISO timestamp of the response.

    Raises:
        HTTPException:
            - 401 if the API key is invalid.
            - 500 if an error occurs during model loading.

    Example Request:
        ```json
        {
            "botId": "test-bot",
            "modelUrl": "/path/to/model.tar.gz"
        }
        ```

    Example Response:
        ```json
        {
            "success": true,
            "data": {
                "botId": "test-bot",
                "loaded": true
            },
            "timestamp": "2025-07-11T17:34:00.123456"
        }
        ```
    """
    try:
        success = await model_service.load_model(request.botId, request.modelUrl)
        
        return {
            "success": True,
            "data": {
                "botId": request.botId,
                "loaded": success
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/v1/models/{bot_id}")
async def unload_model(
    bot_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(verify_api_key)
):
    """
    Unload a Rasa model for a specific bot.

    This endpoint removes a loaded model and cleans up associated resources.

    Parameters:
        bot_id (str): Unique identifier for the bot (path parameter).
        credentials (HTTPAuthorizationCredentials): Bearer token for authentication.

    Returns:
        dict: A JSON response with the following structure:
            - success (bool): Indicates if the request was successful.
            - data (dict): Contains unloading results:
                - botId (str): The bot identifier.
                - unloaded (bool): Indicates if the model was unloaded successfully.
            - timestamp (str): ISO timestamp of the response.

    Raises:
        HTTPException:
            - 401 if the API key is invalid.
            - 500 if an error occurs during model unloading.

    Example Request:
        ```bash
        curl -X DELETE "http://localhost:5005/api/v1/models/test-bot" \
             -H "Authorization: Bearer your-api-key-for-internal-services"
        ```

    Example Response:
        ```json
        {
            "success": true,
            "data": {
                "botId": "test-bot",
                "unloaded": true
            },
            "timestamp": "2025-07-11T17:34:00.123456"
        }
        ```
    """
    try:
        success = await model_service.unload_model(bot_id)
        
        return {
            "success": True,
            "data": {
                "botId": bot_id,
                "unloaded": success
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error unloading model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/models")
async def get_loaded_models(
    credentials: HTTPAuthorizationCredentials = Depends(verify_api_key)
):
    """
    List all currently loaded Rasa models.

    This endpoint returns information about all models currently loaded in the server.

    Parameters:
        credentials (HTTPAuthorizationCredentials): Bearer token for authentication.

    Returns:
        dict: A JSON response with the following structure:
            - success (bool): Indicates if the request was successful.
            - data (dict): Contains model information:
                - models (list): List of loaded models, each with:
                    - botId (str): The bot identifier.
                    - loadedAt (str): ISO timestamp when the model was loaded.
                    - lastUsed (str): ISO timestamp when the model was last used.
                - count (int): Number of loaded models.
            - timestamp (str): ISO timestamp of the response.

    Raises:
        HTTPException:
            - 401 if the API key is invalid.
            - 500 if an error occurs while retrieving the model list.

    Example Response:
        ```json
        {
            "success": true,
            "data": {
                "models": [
                    {
                        "botId": "test-bot",
                        "loadedAt": "2025-07-11T17:34:00.123456",
                        "lastUsed": "2025-07-11T17:34:00.123456"
                    }
                ],
                "count": 1
            },
            "timestamp": "2025-07-11T17:34:00.123456"
        }
        ```
    """
    try:
        models = model_service.get_loaded_models()
        
        return {
            "success": True,
            "data": {
                "models": models,
                "count": len(models)
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting loaded models: {e}")
        raise HTTPException(status_code=500, detail=str(e))