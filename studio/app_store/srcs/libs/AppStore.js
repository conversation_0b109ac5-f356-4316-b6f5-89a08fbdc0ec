const KPI = require("app_kpi");
const utility = require("utility");
const { Sequelize } = require('sequelize');


const DEFAULT_SORT_ORDER = "asc";

const KPI_KEY = KPI.KEYS.appstore;
const oam_alert_oid = "app_store";

const APPS_LIST_ATTRIBUTES = ["id", "name", "desc", "status", "owner", "createdBy", "modifiedBy", "createdAt", "updatedAt", "ngage_id", "alignment", "svg", "OTC", "MRC", "channels", "nodes", "freeNodeExec",
  //  "nodeExecCharge"
  ];
const APPS_INFO_ATTRIBUTES = ["id", "name", "desc", "status", "owner", "createdBy", "modifiedBy", "createdAt", "updatedAt", "appData", "ngage_id", "alignment", "svg", "OTC", "MRC", "channels", "nodes", "freeNodeExec", 
  // "nodeExecCharge"
];
const APPS_LIST_ATTRIBUTES_WITH_SCH_TIME = ["id", "name", "desc", "status", "owner", "createdBy", "modifiedBy", "createdAt", "updatedAt", "scheduleTime"];
const APP_VERSION_ATTRIBUTES = ["appid", "version", "comment", "owner", "createdBy", "createdAt", "updatedAt", "appData", "type", "version_name"];
const APPS_LIST_BYID_ATTRIBUTES = ["id", "name", "status", "createdAt"];
const APPS_LIST_BYNAME_ATTRIBUTES = ["name"];
const APP_LOCALE_LIST_ATTRIBUTES = ["id", "appId", "locale"];
const APP_LOCALE_DATA_ATTRIBUTES = ["id", "appId", "locale", "translation", "createdBy", "modifiedBy", "createdAt", "updatedAt", "deletedAt"];
const DELETED_USERS_LIST_ATTRIBUTES = ["deletedUserId", "firstName", "lastName"];

module.exports = {
  count: async (opts) => {
    let headers = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {},
        page = 1,
        size = Number.MAX_SAFE_INTEGER;
      if (opts != null) {
        page = parseInt(opts.page, 10);
        size = parseInt(opts.size, 10);
        sequel_opts = applyFilters(opts, sequel_opts);
      }
      const totalApps = await global.leapDB.models.AppsMaster.count(sequel_opts);
      size = size > totalApps ? totalApps : size;
      const lastPage = Math.ceil(totalApps / size);
      headers = {
        totalApps: totalApps,
        lastPage: lastPage,
        prevPage: page > 1 ? page - 1 : page,
        nextPage: page < lastPage ? page + 1 : lastPage,
        pageSize: size
      };
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "totalCount");
    } catch (e) {
      global.logger.error("Exception while retreiving apps headers", e);
      global.raiseAlert(e);
    }
    return headers;
  },

  /**
   * @api listApps(opts) // AppList: API for retrieving list of Apps.
   * @apiDescription Fetches a list of apps from the app_store database. The quantity and content of records fetched depends upon the user making this request.
   * If a developer, it will only fetch a list of apps created by him / her. While an administrator may be able to see all apps.
   *
   * @param {JSONObject} opts // Optional parameter with fields token, page, size, sortf, order, user
  Sample:
  opts:{"token": "", "page": "", "size": "", "sortf": "", "order": "", "user": ""};
  * @returns {JSONObject} // Returns the JSON Object which is an Array of apps.
  */
  listApps: async (opts) => {

    let apps = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {
        attributes: APPS_LIST_ATTRIBUTES
      };
      if (opts != null) {
        if (opts.scheduleTimeRequired) {
          sequel_opts.attributes = APPS_LIST_ATTRIBUTES_WITH_SCH_TIME;
        }

        sequel_opts = applyFilters(opts, sequel_opts);
      }
      // Converting the Sequelize object to Plain JSON Object
      global.logger.error("SEQUEL OPTS: " + JSON.stringify(sequel_opts));
      apps = await global.leapDB.models.AppsMaster.findAndCountAll(sequel_opts);
      if (apps != null) {
        apps = JSON.parse(JSON.stringify(apps));
        global.clearAlert();
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listApps");
    } catch (e) {
      global.logger.error("Exception while retreiving apps", e);
      global.raiseAlert(e);
    }
    return apps;
  },

  listUniqueIds: async () => {
    let uniqueNgageIds = null;
    try {
      uniqueNgageIds = await global.leapDB.models.AppsMaster.findAll({
        attributes: [
          [Sequelize.fn('DISTINCT', Sequelize.col('ngage_id')), 'ngage_id']
        ],
        raw: true // This ensures the result is a plain JavaScript object rather than a Sequelize model instance
      });
    } catch (e) {
      global.logger.error("Exception while retrieving unique ngage_id", e);
      global.raiseAlert(e);
    }
    return uniqueNgageIds;
  },

  listLiveApps: async (opts) => {
    let liveApps = null,
      startTime = new Date().getTime();

    try {
      // Construct the raw SQL query
      const escapedAndPrefixedAttributes = APPS_LIST_ATTRIBUTES.map(attr => {
        const prefixedAttr = `AppsMaster.${attr}`;
        return prefixedAttr;
      }).join(', ');

      let rawQuery = `
        SELECT ${escapedAndPrefixedAttributes}
        FROM apps_master AS AppsMaster
        INNER JOIN apps_snapshots AS AppsSnapshots ON AppsSnapshots.appid = AppsMaster.id
        WHERE AppsSnapshots.type = 'Live'
        GROUP BY AppsMaster.id
      `;
      // Adjust the query if additional options are provided
      if (opts != null) {
        // Add additional attributes if scheduleTimeRequired is set
        if (opts.scheduleTimeRequired) {
          rawQuery = rawQuery.replace('SELECT', `SELECT ${APPS_LIST_ATTRIBUTES_WITH_SCH_TIME.join(', ')},`);
        }

        // Apply any additional filters (you need to adjust applyFilters function to work with raw SQL)
        rawQuery = applyFilters(opts, rawQuery);
      }

      // Execute the raw SQL query
      const result = await global.leapDB.query(rawQuery, { type: global.leapDB.QueryTypes.SELECT });
      liveApps = result;

      // Logging and alert management
      if (liveApps != null) {
        global.clearAlert();
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listLiveApps");
    } catch (e) {
      global.logger.error("Exception while retrieving live apps", e);
      global.raiseAlert(e);
    }
    return liveApps;
  },


  /**
  * @api listAppNames(opts) // AppNameList: API for retrieving list of App Names.
  * @apiDescription Fetches a list of app names from the app_store database. The quantity and content of records fetched depends upon the user making this request.
  * If a developer, it will only fetch a list of apps created by him / her. While an administrator may be able to see all apps.
  *
  * @param {JSONObject} opts // Optional parameter with fields token, page, size, sortf, order, user
  Sample:
  opts:{"token": "", "page": "", "size": "", "sortf": "", "order": "", "user": ""};
  * @returns {JSONObject} // Returns the JSON Object which is an Array of app names.
  */
  listAppNames: async (opts) => {
    let apps = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {
        attributes: APPS_LIST_BYNAME_ATTRIBUTES
      };

      if (opts != null) {
        sequel_opts = applyFilters(opts, sequel_opts);
      }
      // Converting the Sequelize object to Plain JSON Object
      apps = await global.leapDB.models.AppsMaster.findAll(sequel_opts);
      apps = JSON.parse(JSON.stringify(apps));
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listApps");
    } catch (e) {
      global.logger.error("Exception while retreiving apps", e);
      global.raiseAlert(e);
    }
    return apps;
  },

  /**
   * @api createApp(app) // CreateApp: API for creating the app in app_store database
   * @apiDescription This api allows the AppDeveloper to create the Application
   * @param {JSONObject} app
   * @returns {Boolean} // Return true on success database operation else false.
   */
  createApp: async (appInfo) => {
    let res;
    let startTime = new Date().getTime();
    try {
      if (appInfo.appData != null) {
        appInfo.appData = await utility.deflateJSON(appInfo.appData, global.jsonsupport);
      }
      appInfo.createdAt = global.getTZFormatDateTime(appInfo.createdAt);
      appInfo.updatedAt = appInfo.createdAt;

      res = await global.leapDB.models.AppsMaster.create(appInfo);
      global.leapDB.models.AppsLocale.create({
        appId: res.id,
        locale: "en",
        createdBy: appInfo.createdBy,
        modifiedBy: appInfo.modifiedBy
      }).then(() => {
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("AppsLocale updated successfully");
        }
      });
      global.leapDB.models.AppsHistory.create({
        appId: res.id,
        appName: appInfo.name,
        event: -1,
        status: appInfo.status,
        remarks: "Application is created on " + res.createdAt,
        performer: appInfo.createdBy,
        createdBy: appInfo.createdBy,
        modifiedBy: appInfo.modifiedBy
      }).then(() => {
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("AppsHistory updated successfully");
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "createApp");
    } catch (e) {
      global.raiseAlert(e);
      throw e;
    }
    return res;
  },

  /**
   * @api bulkCreateApp(apps) // bulkCreateApp: API for creating the bulk apps in app_store database
   * @apiDescription This api allows the AppDeveloper to create the Applications
   * @param {JSONObject} app
   * @returns {Boolean} // Return true on success database operation else false.
   */
  bulkCreateApp: async (bulk) => {
    let res, startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.AppsMaster.bulkCreate(bulk);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "bulkCreateApp");
    } catch (e) {
      global.logger.error("Failed to create apps", e);
      global.raiseAlert(e);
    }
    return res;
  },

  bulkCreateAppReports: async (bulk) => {
    let res, startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.AppsReport.bulkCreate(bulk);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "bulkCreateAppReports");
    } catch (e) {
      global.logger.error("Failed to create apps report", e);
      global.raiseAlert(e);
    }
    return res;
  },
  /**
   * @api updateApp(app) // UpdateApp: API for updating the app in app_store database
   * @apiDescription This api allows the AppDeveloper to update the Application
   * @param {JSONObject} app
   * @returns {Boolean} // Return true on success database operation else false.
   */
  updateApp: async (appInfo) => {
    let startTime = new Date().getTime(),
      result;
    try {
      let app = {
        modifiedBy: appInfo.modifiedBy,
        updatedAt: global.getTZFormatDateTime(appInfo.updatedAt),
        svg: appInfo.svg,
        alignment: appInfo.alignment,
        status: appInfo.status,
        channels: appInfo.channels,
        nodes: appInfo.nodes,
        OTC: appInfo.OTC,
        MRC: appInfo.MRC,
        freeNodeExec: appInfo.freeNodeExec,
        // nodeExecCharge: appInfo.nodeExecCharge
      };
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("ID:" + appInfo.id + ", Updating app: ", appInfo.appData);
      }

      if (appInfo.owner != null) {
        app.owner = appInfo.owner;
      }

      if (appInfo.name != null) {
        app.name = appInfo.name;
      }
      if (appInfo.desc != null) {
        app.desc = appInfo.desc;
      }
      if (appInfo.appData != null) {
        app.appData = await utility.deflateJSON(appInfo.appData, global.jsonsupport);
      }
      result = await global.leapDB.models.AppsMaster.update(app, {
        where: {
          $and: {
            id: appInfo.id
          }
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updateApp");
    } catch (e) {
      global.logger.error("Exception while updating App", e);
      global.raiseAlert(e);
    }
    return result;
  },

  /**
   * @api deleteApp(appId, userId) // DeleteApp: Destroy Application Information
   * @param {String} appId
   * @param {String} userId
   * @apiDescription The scope of application retrieved by this api depends on who is asking.
   * If an AppDeveloper role is invoking this api, it will allow to delete AppInfo which belongs to his/her creation only.
   * If a administrator role is invoking this api, it will allow to delete AppInfo irrespective of owner.
   * @returns {Interger} // Returns number of records deleted
   */
  deleteApp: async (appId, forceDelete = true) => {
    let startTime = new Date().getTime(),
      result = null;
    try {
      let filter = {
        force: forceDelete,
        where: {
          $or: [{
            id: appId
          }, {
            name: appId
          }]
        }
      };

      result = await global.leapDB.models.AppsMaster.destroy(filter);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "deleteApp");
    } catch (e) {
      global.logger.error("Failed to delete App", e);
      global.raiseAlert(e);
    }
    return (result != null && result > 0);
  },

  /**
   * @api UpdateOwnerApp(apps) // UpdateOwnerApp: API for updating the Owner details to unassigned after deleting owner
   *
   */
  updateOwnerApp: async (oldOwnerId, newOwnerId) => {
    let startTime = new Date().getTime()
    try {
      /*
       * Find the entries corresponding to the sourceUserId in the apps_master table
       */
      let search_options = {
        where: {
          owner: oldOwnerId
        }
      };
      let data_to_update = {
        owner: newOwnerId
      };
      const rowCount = await global.leapDB.models.AppsMaster.update(data_to_update, search_options);
      if (global.logger.isTraceEnabled()) {
        if (rowCount > 0) {
          global.logger.trace("App_master updated ", rowCount, " rows successfully");
        } else {
          global.logger.trace("App_master didn't update since row wasn't found");
        }
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "UpdateOwnerApp");
    } catch (e) {
      global.logger.error("Exception in trying to update app_master table ", e);
      global.raiseAlert(e);
    }
  },

  /**
   * @api bulkDeleteApp(apps) // bulkDeleteApp: API for deleting the bulk apps in app_store database
   * @apiDescription This api allows the AppDeveloper to delete the Applications
   * @param {JSONObject} app
   * @returns {Boolean} // Return true on success database operation else false.
   */
  bulkDeleteApp: async (bulk) => {
    let res, startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.AppsMaster.destroy({
        force: true,
        where: {
          $or: [{
            id: bulk
          }, {
            name: bulk
          }]
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "bulkDeleteApp");
    } catch (e) {
      global.logger.error("Failed to Delete Apps", e);
      global.raiseAlert(e);
    }
    return res;
  },

  /**
  * @api bulkDeleteApp(apps) // bulkDeleteApp: API for deleting the bulk apps in app_store database
  * @apiDescription This api allows the AppDeveloper to delete the Applications
  * @param {JSONObject} app
  * @returns {Boolean} // Return true on success database operation else false.
  */
  bulkDeleteAppTemplate: async (bulk) => {
    let res, startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.AppsTemplate.destroy({
        force: true,
        where: {
          $or: [{
            id: bulk
          }, {
            name: bulk
          }]
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "bulkDeleteAppTemplate");
    } catch (e) {
      global.logger.error("Failed to Delete Apps", e);
      global.raiseAlert(e);
    }
    return res;
  },

  /**
   * @api findApp(appId), AppInfo: Get Application Information
   * @param {String} appId
   * @apiDescription The scope of applications retrieved by this api depends on who is asking.
   * If an AppDeveloper role is invoking this api, it will return a AppInfo JSON Object which belongs to his/her creation only.
   */
  findApp: async (appId) => {
    let startTime = new Date().getTime(),
      app = null;
    try {
      let filter = {
        attributes: APPS_INFO_ATTRIBUTES
      };
      filter = Object.assign(filter, {
        where: {
          $or: [{
            id: appId
          }, {
            name: appId
          }]
        }
      });

      app = await global.leapDB.models.AppsMaster.findOne(filter);
      if (app != null) {
        app = JSON.parse(JSON.stringify(app));
        app.appData = await utility.unzipJSON(app.appData, global.jsonsupport);
        app.status = String(app.status);
        app.createdAt = global.getTZFormatDateTime(app.createdAt);
        app.updatedAt = global.getTZFormatDateTime(app.updatedAt);
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findApp");
    } catch (e) {
      global.logger.error("Failed to Retrieve App: ", appId, e);
      global.raiseAlert(e);
    }
    return app;
  },

  findLiveApp: async (appId) => {
    let liveApp = null,
      app = null,
      startTime = new Date().getTime();

    try {
      // Construct the raw SQL query
      const escapedAndPrefixedAttributes = APPS_INFO_ATTRIBUTES.map(attr => {
        let prefixedAttr = `AppsMaster.${attr}`;
        if (attr == 'appData' || attr == 'updatedAt') {
          prefixedAttr = `AppsSnapshots.${attr}`;
        }
        return prefixedAttr;
      }).join(', ');

      let rawQuery = `
        SELECT ${escapedAndPrefixedAttributes}
        FROM apps_master AS AppsMaster
        INNER JOIN apps_snapshots AS AppsSnapshots ON AppsSnapshots.appid = AppsMaster.id
        WHERE AppsMaster.id = '${appId}' and AppsSnapshots.type = 'Live'
        ORDER BY AppsSnapshots.updatedAt
        DESC LIMIT 1
      `;

      liveApp = await global.leapDB.query(rawQuery, { type: global.leapDB.QueryTypes.SELECT });
      app = liveApp[0];

      if (app != null) {
        app = JSON.parse(JSON.stringify(app));
        app.appData = await utility.unzipJSON(app.appData, global.jsonsupport);
        app.status = String(app.status);
        app.createdAt = global.getTZFormatDateTime(app.createdAt);
        app.updatedAt = global.getTZFormatDateTime(app.updatedAt);
        global.clearAlert();
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listLiveApp");
    } catch (e) {
      global.logger.error("Exception while retrieving live apps", e);
      global.raiseAlert(e);
    }
    return app;
  },

  findCompressedApp: async (appId) => {
    let startTime = new Date().getTime(),
      app = null;
    try {
      let filter = {
        attributes: APPS_INFO_ATTRIBUTES
      };
      filter = Object.assign(filter, {
        where: {
          $or: [{
            id: appId
          }, {
            name: appId
          }]
        }
      });

      app = await global.leapDB.models.AppsMaster.findOne(filter);
      if (app != null) {
        app = JSON.parse(JSON.stringify(app));
        app.appData = await utility.deflateJSON(app.appData, global.jsonsupport);
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findCompressedApp");
    } catch (e) {
      global.logger.error("Failed to Retrieve Compressed App", e);
      global.raiseAlert(e);
    }
    return app;
  },

  listAppsById: async (appsId, status) => {
    let apps = null,
      startTime = new Date().getTime();
    try {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Executing Listing apps for ID's:" + appsId);
      }
      let sequel_opts = {
        attributes: APPS_LIST_BYID_ATTRIBUTES
      };

      if (status != null && appsId != null) {
        sequel_opts.order = [
          ['updatedAt', 'DESC']
        ];
        sequel_opts = Object.assign(sequel_opts, {
          where: {
            status: status,
            id: {
              $or: appsId
            }
          }
        })
      } else if (appsId != null) {
        sequel_opts = Object.assign(sequel_opts, {
          where: {
            id: {
              $or: appsId
            }
          }
        });
      }
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Sequel options:" + JSON.stringify(sequel_opts));
      }
      apps = await global.leapDB.models.AppsMaster.findAll(sequel_opts);
      apps = JSON.parse(JSON.stringify(apps));
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Listed apps:" + JSON.stringify(apps));
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "ListAppsByID");
    } catch (e) {
      global.logger.error("Exception occured while Listing Apps By ID's", e);
      global.raiseAlert(e);
    }
    return apps;
  },

  listDeletedUsers: async (opts) => {
    let deletedUsers = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {
        attributes: DELETED_USERS_LIST_ATTRIBUTES
      };

      if (opts != null) {
        sequel_opts = applyFilters(opts, sequel_opts);
      }
      // Converting the Sequelize object to Plain JSON Object
      deletedUsers = await global.leapDB.models.DeletedUsers.findAll(sequel_opts);
      deletedUsers = JSON.parse(JSON.stringify(deletedUsers));
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listDeletedUsers");
    } catch (error) {
      global.logger.error("Exception while retreiving apps", error);
    }
    return deletedUsers;
  },

  getAppLocaleTemplate: async (appId, locale) => {
    let data = {},
      startTime = new Date().getTime();
    try {
      locale = locale || "en";
      data = await global.leapDB.models.AppsLocale.findOne({
        where: {
          $and: [{
            appId: appId
          }, {
            locale: locale
          }]
        }
      });

      // Converting the Sequelize object to Plain JSON Object
      data = data && JSON.parse(JSON.stringify(data)) || {};
      if (global.jsonsupport) {
        if (typeof data.translation == "string") {
          data.translation = JSON.parse(data.translation);
        }
      } else {
        data.translation = await utility.unzipJSON(data.translation, global.jsonsupport);
      }


      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getAppTemplates");
    } catch (e) {
      global.logger.error("Failed to retreive App locale", e);
      global.raiseAlert(e);
    }
    return data;
  },

  /**
   * @api updateApp(app) // UpdateApp: API for updating the app in app_store database
   * @apiDescription This api allows the AppDeveloper to update the Application
   * @param {JSONObject} app
   * @returns {Boolean} // Return true on success database operation else false.
   */
  updateAppLocale: async (app) => {
    let flag = false,
      startTime = new Date().getTime();
    try {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("ID:", app.id, ",Updating applocale: ", app.translation);
      }

      let status;
      let data = await global.leapDB.models.AppsLocale.findOne({ where: { $and: { appId: app.id, locale: app.locale } } });
      app.updatedAt = global.getTZFormatDateTime(app.updatedAt);
      if (data == null) {
        app.appId = app.id;
        delete app.id;
        app.createdBy = app.modifiedBy;
        app.createdAt = app.updatedAt;
        app.translation = await utility.deflateJSON(app.translation, global.jsonsupport),
          status = await global.leapDB.models.AppsLocale.create(app);
        if (status) flag = true;
      } else {
        data = JSON.parse(JSON.stringify(data));
        status = await global.leapDB.models.AppsLocale.update({
          translation: await utility.deflateJSON(app.translation, global.jsonsupport),
          modifiedBy: app.modifiedBy,
          updatedAt: app.updatedAt
        }, {
          where: {
            $and: {
              appId: app.id,
              locale: app.locale
            }
          }
        });

        if (status[0] > 0) flag = true;
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updateAppLocale");
    } catch (e) {
      global.logger.error("Failed to update the App locale", e);
      global.raiseAlert(e);
    }
    return flag;
  },

  listAppLocale: async (appId, options) => {
    let startTime = new Date().getTime();
    try {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Listing app locale for:" + appId);
      }
      let attributes = options != null && options.download && APP_LOCALE_DATA_ATTRIBUTES || APP_LOCALE_LIST_ATTRIBUTES;
      let locales = await global.leapDB.models.AppsLocale.findAll({
        attributes,
        where: {
          appId: appId
        }
      });
      if (locales != null) {
        locales = JSON.parse(JSON.stringify(locales));
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "ListApplocale");
      return locales;
    } catch (e) {
      global.logger.error("Failed to list the App locale", e);
      global.raiseAlert(e);
      throw new Error("Exception occured while listing locale", e);
    }
  },

  deleteAppLocale: async (localeId) => {
    let startTime = new Date().getTime();
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Deleting app locale for:" + localeId);
    }
    try {
      let result = await global.leapDB.models.AppsLocale.destroy({
        where: {
          $and: {
            id: localeId
          }
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "DeleteApplocale");
      return (result != null && result > 0);
    } catch (e) {
      global.logger.error("Failed to Delete the App locale", e);
      global.raiseAlert(e);
      throw new Error("Exception occured while listing locale", e);
    }
  },

  addDeletedUser: async (deletedUserInfo) => {
    let res;
    let startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.DeletedUsers.create(deletedUserInfo);
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "addDeletedUser");
    } catch (error) {
      global.logger.error(error);
    }
    return res;
  },

  /**
   * @api acl_link(list) // acl_link : for linking ACL lists to the menus in appication
   * @apiDescription This api allows the AppDeveloper to link ACL Lists to the menus
   * @param {JSONObject} list: which contains app_id, menu_id, menu_item_id and list_id
   * @returns {Boolean} // Return true on success database operation else false.
   */

  acl_link: async (app_id, menu_id, opts) => {
    let result = [];
    try {
      if (menu_id != null && app_id != null) {
        await global.leapDB.models.associative_acl_list.destroy({
          where: {
            menu_id: menu_id,
            $and: { aid: app_id }
          }
        });
        opts.forEach((entry) => {
          if (entry.list_id != null) {
            entry.list_id.forEach((item) => {
              result = global.leapDB.models.associative_acl_list.create({
                aid: app_id,
                menu_id: menu_id,
                menu_item_id: entry.menu_item_id,
                list_id: item
              });
            });
          }
          else {
            return result;
          }
        });
      }
    }
    catch (e) {
      global.logger.error("Failed to link ACL list", e);
      global.raiseAlert(e);
    }
    return result;
  },

  /**
  * @api associative_list(lid) // associate_list: for checking associativity of list to menu.
  * @apiDescription This api allows the AppDeveloper for checking if list is associated to any menu.
  * @param {JSONObject} lid: list_id of list whose associativity is to be checked.
  * @returns {Boolean} // Return json object of menu associated to the list or null.
  */

  associative_list: async (lid) => {
    let result;
    try {
      if (lid != null) {
        let attributes = ["aid", "menu_id", "menu_item_id"];
        let listSeqOpts = {
          attributes,
          where: { list_id: lid }
        };
        result = await global.leapDB.models.associative_acl_list.findAll(listSeqOpts);
      }
      if (result != null) {
        result = JSON.parse(JSON.stringify(result));
        return result;
      }
      else {
        result = [];
        return result;
      }
    }
    catch (e) {
      global.logger.error("Failed check associativity of ACL list", e);
      global.raiseAlert(e);
    }
  },

  /**
   * @api delete_associated_list(appid) //delete_associated_list : for deleting associated list if app is deleted.
   * @apiDescription This api allows the AppDeveloper to delete associated ACL list if app is deleted.
   * @param appid : Corresponding to this appid, associated ACL lists will be deleted.
   * @returns {number} // number of rows deleted.
   */

  delete_associated_list: async (appid) => {
    let result;
    try {
      if (appid != null) {
        result = await global.leapDB.models.associative_acl_list.destroy({
          where: {
            aid: appid
          }
        });
        if (result != null) {
          result = JSON.parse(JSON.stringify(result));
        }
        return result;
      }
    }
    catch (e) {
      global.logger.error("Failed to delete ACL list", e);
      global.raiseAlert(e);
    }
  }
};

function getSortOptions(opts, sequel_opts) {
  if (opts.sortf) {
    let sortArray = [];
    if (opts.order) {
      sortArray.push(opts.sortf);
      sortArray.push(opts.order);
    } else {
      sortArray.push(opts.sortf);
      sortArray.push(DEFAULT_SORT_ORDER);
    }
    sequel_opts.order = [sortArray];
  }
  return sequel_opts;
}

function setDefaultOptions(opts, sequel_opts) {

  if (opts.user == null) {
    opts.user = {
      $like: "%"
    };
  }
  if (opts.owner == null) {
    opts.owner = {
      $like: "%"
    };
  }

  if (opts.status == null) {
    opts.status = {
      $like: "%"
    };
  } else {
    try {
      opts.status = {
        $in: opts.status.split(",")
      };
    } catch (e) { }
  }
  return sequel_opts;
}

function getPaginationOptions(opts, sequel_opts) {

  let pageno = opts.page ? parseInt(opts.page, 10) : 1;
  if (pageno <= 0) {
    pageno = 1;
  }
  sequel_opts.limit = opts.size ? parseInt(opts.size, 10) : 10;
  sequel_opts.offset = (pageno - 1) * sequel_opts.limit;
  return sequel_opts;
}

function getFilteringOptions(opts, sequel_opts) {

  let tokenFilter = [];
  if (opts.token && opts.token.trim().length > 0) {
    opts.token.split(",").forEach((item) => {
      let token = item.trim();
      if (token.length > 0) {
        tokenFilter.push({
          id: {
            $like: "%" + token + "%"
          }
        });
        tokenFilter.push({
          name: {
            $like: "%" + token + "%"
          }
        });
      }
    });
  } else {
    tokenFilter.push({
      id: {
        $like: "%"
      }
    });
    tokenFilter.push({
      name: {
        $like: "%"
      }
    });
  }
  sequel_opts.where = {
    $and: {
      createdBy: opts.user,
      status: opts.status,
      owner: opts.owner,
      $or: tokenFilter
    }
  };
  if (typeof opts.ngage_id !== 'undefined' || opts.ngage_id) {
    sequel_opts.where = {
      $and: {
        createdBy: opts.user,
        status: opts.status,
        owner: opts.owner,
        ngage_id: opts.ngage_id,
        $or: tokenFilter
      }
    };
  }
  return sequel_opts;
}

function getDateRangeOptions(opts, sequel_opts) {
  if (opts.startTime && opts.endTime) {
    let startTime = new Date(Number(opts.startTime));
    let endTime = new Date(Number(opts.endTime));
    sequel_opts.where = Object.assign(sequel_opts.where.$and, {
      createdAt: {
        $between: [startTime, endTime]
      }
    });
  }
  if (opts.startTime && opts.endTime) {
    let startTime = new Date(Number(opts.startTime));
    let endTime = new Date(Number(opts.startTime));
    sequel_opts.where = Object.assign(sequel_opts.where.$and, {
      updatedAt: {
        $between: [startTime, endTime]
      }
    });
  }
  return sequel_opts;
}

function applyFilters(opts, sequel_opts) {
  sequel_opts = getSortOptions(opts, sequel_opts);
  sequel_opts = setDefaultOptions(opts, sequel_opts);
  sequel_opts = getPaginationOptions(opts, sequel_opts);
  sequel_opts = getFilteringOptions(opts, sequel_opts);
  sequel_opts = getDateRangeOptions(opts, sequel_opts);
  return sequel_opts;
}
