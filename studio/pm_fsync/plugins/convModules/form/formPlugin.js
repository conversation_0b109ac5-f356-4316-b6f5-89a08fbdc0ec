"use strict";
/**
 *  Form Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class FormPlugin
 */
class FormPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        // Initialize journey context
        this.updateJourneyContext(context);
        const {journeyContext} = context

        // Handle user input if this is a resumed execution
        //TODO: need to check if it's needed at all (might need this in case for different channelType)
        // if (context.input?.userInput) {
        // this.handleUserInput(context, context.input.userInput);
        // }


        const formConfig = context.process.formConfig;
        const formId = context.process.formId;
        const isSingleForm = formConfig.formType === 'single-ask-form'
        const userInput = journeyContext?.formData?.[formId] || {};
        const currentFields = this.getAlPendingFields(formConfig, userInput,isSingleForm?1:0);

        if (currentFields.length) {
          // Need more input - pause execution
          const prompt = currentFields.map((currentField) => ({
            fieldName: currentField.name,
            fieldType: currentField.type,
            label: currentField.label,
            required: currentField.required,
            options: currentField.options,
            formId,
            value:userInput[currentField] //TODO: might need to remove this
          }))
          const formNodeData={
            prompt,
            formId,
            formType:'single-ask-form'
          }
          const result = {
            code: error_codes.success,
            awaitingInput: true,
            nodeType: "form",
            data:formNodeData,
            journeyContext
          };

          // Update journey context
          this.updateJourneyContext(context, {
            awaitingInput: true,
            currentForm:formNodeData
          });

          resolve(result);
        } else {
          // All fields collected - form complete
          const result = {
            code: error_codes.success,
            formComplete: true,
            data: {
              formId: formId,
              submittedData: userInput,
              submissionTime: new Date().toISOString(),
            },
          };

          // Update journey context
          this.updateJourneyContext(context, {
            completedForms: [...(context.completedForms ?? []), formId],
            awaitingInput: false,
            currentForm: null,
          });

          resolve(result);
        }
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  getCurrentField(formConfig, userInput) {
    if (!formConfig?.fields) return null;

    for (const field of formConfig.fields) {
      if (!userInput.hasOwnProperty(field.name)) {
        return field;
      }
    }
    return null;
  }

  getAlPendingFields(formConfig, userInput, fieldsLength) {
    const fields = [];
    if (!formConfig?.fields) return fields;

    for (const field of formConfig.fields) {
      if (!userInput.hasOwnProperty(field.name)) {
        fields.push(field);
        if(fieldsLength && fields.length === fieldsLength)return fields
      }
    }
    return fields;
  }

  close() {}

  /**
   * Handle user input for resumed execution
   * @param {any} context Execution context
   * @param {any} userInput User input data
   */
  handleUserInput(context, userInput) {
    if (!context.journeyContext) {
      context.journeyContext = {};
    }

    const { currentForm } = context.journeyContext;

    // Store form data if it's a form input
    if (currentForm && userInput.fieldName) {
      if (!context.journeyContext.formData) {
        context.journeyContext.formData = {};
      }
      if (!context.journeyContext.formData[currentForm]) {
        context.journeyContext.formData[currentForm] = {};
      }
      context.journeyContext.formData[currentForm][userInput.fieldName] =
        userInput.value;
    }

    // Update last input
    context.journeyContext.lastInput = userInput;
    context.journeyContext.lastInputTime = new Date().toISOString();
  }
}

module.exports = FormPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./formSchema.json");
  schema.category = "form";
  return schema;
}
