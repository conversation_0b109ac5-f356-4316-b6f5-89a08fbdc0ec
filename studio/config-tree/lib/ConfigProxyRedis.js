const events = require("events");
const RedisManager = require("./redis_man");
const BPromise = require("bluebird");
const subscriptionKey = "__key*__:system:settings:*";
require("colors");
const ConfigTasks = require("./ConfigTasksRedis");
const PrintUtils = require("./print");
const CountryCodes = require("./country_codes");
const ECPFSM = require("./ecp_fsm");

var config = {};
var redisOpts;
class ConfigProxyRedis extends events.EventEmitter {
  constructor(opts) {
    super();
    redisOpts = opts;
    this.configargs = opts.args;

    RedisManager.init({
      config: this.configargs
    });
  }

  async register() {

    RedisManager.init({
      key: "subscriber",
      config: this.configargs
    });
    this.subscriber = await RedisManager.getConnection("subscriber");
    console.warn("Subscribing to Redis keyspace:", subscriptionKey);
    this.subscriber.select(this.configargs.db || 0);
    this.subscriber.psubscribe(subscriptionKey, () => {
      this.subscriber.on("pmessage", async (pattern, channel, key) => {
        if (["hset", "hmset", "del"].includes(key) && containsTaskName(channel)) {
          await this.readConfig(true);
          this.emit("reload_config", pattern, channel, key);
        }
      });
    });
  }

  async readConfig(isReload = false) {
    try {
      config = {};
      const conn = await RedisManager.getConnection();
      await conn.config("SET", "notify-keyspace-events", "KEA");
      await this.register();
      // Iterate over keys2read, and read all of them
      return BPromise.reduce(redisOpts.configTasks, execTask, redisOpts.configTasks[0])
        .then(() => {
          if (!global.config) {
            global.config = {};
          }
          let text = isReload ? "Reloading" : "Loading";
          Object.keys(config).forEach(key => {
            if (config.hasOwnProperty(key)) {
              console.log("%s the configurtion global.config.%s".green, text, key);
              global.config[key] = config[key];
            }
          });
        }).catch(e => {
          console.error("Failed to load Config-Tree", e);
          return BPromise.reject("Failed to read configuration data" + e, e);
        });
    } catch (error) {
      return BPromise.reject("Failed to Init connection to Config-Tree, " + error);
    }
  }

  /**
     *  Uploads data to Redis configuration server
     *  !!! Use with caution !!!
     *  May override existing data without any intimation whatsoever.
     **/
  async publish(type, file) {
    try {

      const FSM_KEY = "system:settings:global:fsm";
      const COUNTRIES_KEY = "system:settings:global:countries";

      // resolve the file to an abs path
      let configLayout = require(file);
      const conn = await RedisManager.getConnection();
      await conn.flushdb();
      type = type || "0";
      switch (type) {
        case "1":
          return BPromise.all(upload(conn, configLayout));
        case "2":
          return BPromise.all(upload(conn, ECPFSM, FSM_KEY));
        case "3":
          return BPromise.all(upload(conn, CountryCodes, COUNTRIES_KEY));
        case "0":
          return BPromise.all(upload(conn, configLayout))
            .then(() => {
              return BPromise.all(
                upload(conn, ECPFSM, FSM_KEY));
            })
            .then(() => {
              return BPromise.all(
                upload(conn, CountryCodes, COUNTRIES_KEY));
            });
      }
    } catch (error) {
      return BPromise.resolve(false, error);
    }
  }

  async flushDB() {
    try {
      const conn = await RedisManager.getConnection();
      conn.flushdb();
      return BPromise.resolve(null);
    } catch (error) {
      return BPromise.resolve(error);
    }
  }

  async close() {
    try {
      await RedisManager.close();
      return BPromise.resolve(null);
    } catch (error) {
      return BPromise.resolve(error);
    }
  }
}

module.exports = ConfigProxyRedis;

function containsTaskName(pattern) {
  let keys = Object.keys(config);
  for (let i = 0; i < keys.length; i++) {
    if (pattern.includes(keys[i])) return true;
  }
  return false;
}

/**
 *  Uploads configuration to redis, by traversign the config tree
 *  @param {Object} conn -- redis connection object
 *  @param {JSONObject} config -- Configuration JSON object that needs to be uploaded.
 *  @param {String} parent -- A string identifying the key for this object
 **/
function upload(conn, config, parent, promises) {
  parent = parent || ""; // if not defined, initialize to an empty string.
  promises = promises || []; // if not defined, initialize to an empty array

  let hash = {};
  for (let k in config) {
    // Use hasOwnProperty to check if the property belongs directly to the config object
    if (config.hasOwnProperty(k)) {
      let dataType = typeof config[k];
      if (dataType === "string" || dataType === "number" || dataType === "boolean") {
        hash[k] = config[k];
      } else {
        let key = parent ? parent + ":" : "";
        upload(conn, config[k], (key + k), promises);
      }
    }
  }

  if (!isEmpty(hash)) {
    console.log("Creating hash for key:", parent);
    promises.push(conn.hmset(parent, hash));
    conn.hmset(parent, hash);
  }
  return promises;
}


function isEmpty(obj) {
  for (let prop in obj) {
    if (obj.hasOwnProperty(prop))
      return false;
  }
  return true;
}

function execTask(prev, task) {
  let taskItems = redisOpts.keys2read[task];
  // Iterate through task items to read configuration sequentially.
  return BPromise.reduce(taskItems, (fn, item) => {
    process.stdout.write("Reading configuration for '" + item + "'...");
    return ConfigTasks[fn](item)
      .then(result => {
        if (result === null) {
          PrintUtils.printFail(item);
          throw new Error("Data not found for " + item + ", using fn:" + fn);
        }
        PrintUtils.printOK(item);
        config[item] = result;
        return fn;
      });
  }, task);
}
