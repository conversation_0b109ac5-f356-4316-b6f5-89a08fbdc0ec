"use strict";
const RedisStore = require("ioredis");
let redisPool = {};
const DEFAULT_KEY = "config-tree";
module.exports = {
  init: (props) => {
    if (!props) {
      props = {
        key: DEFAULT_KEY,
        config: {
          host: "127.0.0.1",
          port: 6379,
          db: 0
        }
      };
    }
    if (!props.key) {
      props.key = DEFAULT_KEY;
    }
    redisPool[props.key] = {
      config: props.config,
      redis: null,    // initial value, when no connection is yet attempted.
      status: 0       // status of connection.
    };
  },

  getConnection: (key = DEFAULT_KEY) => {
    return new Promise((resolve, reject) => {
      const conn = redisPool[key];
      if (conn && conn.redis != null && conn.status == 1) {
        resolve(conn.redis);
      } else {
        conn.redis = new RedisStore(conn.config);

        conn.redis.setMaxListeners(100);

        conn.redis.on("ready", () => {
          conn.status = 1;
          return resolve(conn.redis);
        });

        conn.redis.on("error", (e) => {
          conn.redis = null;
          conn.status = 0;
          e.config = conn.config;
          return reject(e);
        });
      }
    });
  },

  close: async () => {
    let report = {}; // Initialize the report object
    for (const key of Object.keys(redisPool)) { // Iterate over keys of redisPool
      try {
        if (redisPool[key].status == 1) { // Check if the Redis connection is active
          report[key] = await redisPool[key].redis.close(); // Await the close operation
        }
      } catch (error) {
        // Optional: handle the error (e.g., log it)
        console.error(`Error closing redis connection for ${key}:`, error);
      }
    }
    return report; // Return the report object after all operations
  }
  
};
