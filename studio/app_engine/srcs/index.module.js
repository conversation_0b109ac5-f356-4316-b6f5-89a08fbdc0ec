"use strict";

const path = require("path");
const fs = require("fs");
const ConfigTree = require("config-tree");
const utility = require("utility");
const xml2json = require("xml2json");
const tls = require("tls");
const net = require("net");
const { constants } = require("crypto");
const KPI = require("app_kpi");
const OAM = require("oam");
const os = require("os");
const common = require("common");
const message = require("message");
const whiteboard = common.whiteboard;
const redis_man = common.redis_man;
const ENUMS = require("./lib/enums");

global.logger = console;
global.componentName = "app_engine";
global.leap_settings = require("../config/settings.json");

/**
 * Initializes basic configuration for the LEAP platform
 * @param {Object} options - Configuration options
 * @param {string} [options.host="127.0.0.1"] - Config server host
 * @param {number} [options.port=6380] - Config server port
 * @param {number} [options.db=0] - Config server database index
 * @param {string} [options.password] - Config server password
 * @param {string} [options.type="development"] - Server type (development/staging/production)
 * @returns {Promise<ConfigTree>} ConfigTree instance
 */
async function initializeBasicConfig(options = {}) {
  const defaultOptions = {
    host: process.env.CONFIG_HOST || "127.0.0.1",
    port: process.env.CONFIG_PORT || 6380,
    db: process.env.CONFIG_DB || 0,
    password: process.env.CONFIG_AUTH || undefined,
    type: process.env.LEAP_APP_ENGINE || "development",
  };

  const config = { ...defaultOptions, ...options };
  global.mode = config.type;
  setServerMode();

  global.opts = {
    args: config,
    configTasks: ["getDBInfo", "getModuleInfoEx", "getGlobalInfo"],
    keys2read: {
      getDBInfo: ["app_store"],
      getModuleInfoEx: [
        "app_engine",
        "app_engine_" + config.type,
        "pm_fsync",
        "acl_manager",
      ],
      getGlobalInfo: [
        "whiteboard",
        "timezone",
        "kpi",
        "oam",
        "certificates",
        "security",
        "authSecret",
      ],
    },
  };

  const ConfigProxy = new ConfigTree(global.opts);
  await ConfigProxy.readConfig();
  setConfigurations();

  return ConfigProxy;
}

/**
 * Sets up global configurations based on loaded config
 */
function setConfigurations() {
  global.config.instance = global.config["app_engine_" + global.mode];
  delete require.cache[require.resolve("./logger")];
  require("./logger").init();
  global.config.defaultLanguage = global.config.app_engine.defaultLanguage;

  if (global.config.app_engine.plugin_exec) {
    global.httpClientSettings = global.config.app_engine.plugin_exec.client;
    global.peThrottleValue =
      global.config.app_engine.plugin_exec.thresholdValue;
  } else {
    global.httpClientSettings = {
      maxSockets: 100,
      keepAlive: true,
      maxFreeSockets: 10,
    };
    global.peThrottleValue = -1;
  }
}

/**
 * Sets server mode flags based on global.mode
 * @throws {Error} If invalid execution mode is provided
 */
function setServerMode() {
  global.isDevServer = false;
  global.isStagingServer = false;
  global.isProductionServer = false;

  switch (global.mode) {
    case "development":
      global.isDevServer = true;
      break;
    case "staging":
      global.isStagingServer = true;
      break;
    case "production":
      global.isProductionServer = true;
      break;
    default:
      throw new Error(
        "Invalid Execution Mode - Possible values are development, staging and production"
      );
  }
}

/**
 * Initializes all required services for the LEAP platform
 * @returns {Promise<Object>} Service initialization result
 * @returns {number} returns.totalApps - Total number of loaded apps
 * @returns {string} returns.mode - Current server mode
 */
async function initializeServices() {
  await OAM.init(global.config.oam);

  if (global.isProductionServer) {
    await KPI.init({
      app_engine_kpi_enabled: true,
      ...global.config.kpi,
      interval: 1000,
      policy: "file",
      thresholdHeapMemory: global.leap_settings.thresholdHeapMemory,
    });
  }

  whiteboard.init(global.config.whiteboard);
  await require("./lib/cdrqueue").init();
  await require("./cache/UserSessionStore").init();
  await require("pluginmanager").init();
  await require("./lib/plugin_settings").init(
    global.isProductionServer ? "prod" : "dev"
  );
  await require("app_store").init();

  if (global.config.app_engine.userPreferenceStoreCheck) {
    await require("preference_store").init();
  }

  const AppCache = require("./cache/AppCache");
  await AppCache.registerEvents();
  await AppCache.loadApps();

  if (global.isProductionServer) {
    const UssdServiceCache = require("./cache/UssdServiceCache");
    await UssdServiceCache.registerEvents();
    await UssdServiceCache.loadUssdServices();
    const UssdGateWayService = require("./ussd_service/index");
    UssdGateWayService.init();
  }

  message.init({
    autoReload: true,
    defaultLocale: global.config.defaultLanguage,
    defaultKey: "E9000",
  });

  return {
    totalApps: AppCache.totalApps(),
    mode: global.mode,
  };
}

/**
 * Tests TCP connection to specified host and port
 * @param {string} host - Target host address
 * @param {number} port - Target port number
 * @returns {Promise<void>} Resolves if connection successful
 * @throws {Error} If connection fails or times out
 */
async function checkTelnetConnection(host, port) {
  return new Promise((resolve, reject) => {
    const socket = new net.Socket();
    socket.setTimeout(5000);

    socket.once("connect", () => {
      socket.destroy();
      resolve();
    });

    socket.once("error", (err) => {
      socket.destroy();
      reject(err);
    });

    socket.once("timeout", () => {
      socket.destroy();
      reject(new Error("Connection timeout"));
    });

    socket.connect(port, host);
  });
}

/**
 * Sets up health check monitoring system
 */
function setupHealthCheck() {
  global.health = { report: {} };

  whiteboard.on("health_check", (msg) => {
    if (msg == "all" || msg == os.hostname()) {
      global.health.hostname = os.hostname();
      global.health.mode = global.mode;
      global.health.updatedAt = Date.now();
      global.health.report.redis = redis_man.health();
      whiteboard.publish("health_report", JSON.stringify(global.health));
    }
  });

  whiteboard.subscribe("health_check");
}

/**
 * Creates locale settings with fallback to default language
 * @param {string} [locale] - Requested locale
 * @returns {string} Resolved locale string
 */
function createLocaleSettings(locale) {
  return locale || global.config.defaultLanguage;
}

/**
 * Decodes CPS XML message body
 * @param {string|Object} body - Request body
 * @param {Object} headers - HTTP headers
 * @param {string} [headers.content-type] - Content type header
 * @returns {Object} Decoded body object
 */
function createCpsDecoder(body, headers) {
  if (headers["content-type"] && headers["content-type"].includes("xml")) {
    body = xml2json.toJson(body);
    body = JSON.parse(body)["cps-message"];
  }
  return body;
}

/**
 * Preprocesses HTTP request data into query string format
 * @param {string} method - HTTP method (GET, POST, etc.)
 * @param {Object} headers - HTTP headers
 * @param {string} [headers.content-type] - Content type header
 * @param {Object} query - Query parameters
 * @param {Object} [body] - Request body (for POST requests)
 * @returns {Object} Processed query string object with imethod, imode, and merged data
 */
function createPreProcessor(method, headers, query, body) {
  let qs = {
    imethod: method,
    imode: ENUMS.INTERFACES.QUERY_STRING,
    ...headers,
    ...query,
  };

  if (method == "POST") {
    if (headers["content-type"] && headers["content-type"].includes("json")) {
      qs = { ...qs, ...body };
      qs.contentType = "json";
      qs.imode = ENUMS.INTERFACES.HTTP_JSON;
    } else if (
      headers["content-type"] &&
      headers["content-type"].includes("x-www-form-urlencoded")
    ) {
      qs = { ...qs, ...body };
    }
  }

  return qs;
}

/**
 * Main platform initialization function
 * @param {Object} [options={}] - Initialization options
 * @param {string} [options.host="127.0.0.1"] - Config server host
 * @param {number} [options.port=6380] - Config server port
 * @param {number} [options.db=0] - Config server database index
 * @param {string} [options.password] - Config server password
 * @param {string} [options.type="development"] - Server type
 * @param {boolean} [options.checkConnections=true] - Whether to check external connections
 * @returns {Promise<Object>} Initialization result
 * @returns {boolean} returns.success - Whether initialization succeeded
 * @returns {number} returns.totalApps - Total number of loaded apps
 * @returns {string} returns.mode - Current server mode
 * @returns {ConfigTree} returns.configProxy - Configuration proxy instance
 * @throws {Error} If initialization fails
 */
async function initializePlatform(options = {}) {
  try {
    const configProxy = await initializeBasicConfig(options);

    if (options.checkConnections !== false) {
      await checkTelnetConnection(
        global.config.app_store.host,
        global.config.app_store.port
      );
      await checkTelnetConnection(
        global.config.pm_fsync.host,
        global.config.pm_fsync.port
      );
    }

    const serviceInfo = await initializeServices();
    setupHealthCheck();

    return {
      success: true,
      totalApps: serviceInfo.totalApps,
      mode: serviceInfo.mode,
      configProxy,
    };
  } catch (error) {
    throw new Error(`Failed to initialize platform: ${error}`);
  }
}

/**
 * Calculates optimal cluster size based on available CPUs and configuration
 * @returns {number} Recommended cluster size
 */
function getClusterSize() {
  const cpus = os.cpus().length;
  let configuredCPUs = 1;

  if (global.isProductionServer) {
    if (global.config.instance.clusterCPUs > cpus) {
      configuredCPUs = cpus;
    } else {
      configuredCPUs = global.config.instance.clusterCPUs;
    }
  }

  return Math.min(configuredCPUs, cpus);
}

/**
 * Creates TLS/SSL options for HTTPS server
 * @returns {Object} TLS options object
 * @returns {string} returns.key - Private key content
 * @returns {string} returns.cert - Certificate content
 * @returns {number} returns.secureOptions - SSL security options bitmask
 * @returns {string} returns.ciphers - Allowed cipher suites
 * @returns {boolean} returns.honorCipherOrder - Whether to honor cipher order
 */
function createTlsOptions() {
  return {
    key: fs.readFileSync(path.resolve(global.config.certificates.key), "utf8"),
    cert: fs.readFileSync(
      path.resolve(global.config.certificates.cert),
      "utf8"
    ),
    secureOptions:
      constants.SSL_OP_NO_SSLv2 |
      constants.SSL_OP_NO_SSLv3 |
      constants.SSL_OP_NO_TLSv1 |
      constants.SSL_OP_NO_TLSv1_1,
    ciphers:
      "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:",
    honorCipherOrder: true,
  };
}

/**
 * Generates unique transaction ID
 * @returns {string} Unique transaction ID
 */
function generateTxnId() {
  return utility.getUniqueTxnId();
}

/**
 * Validates HTTP method against allowed methods
 * @param {string} method - HTTP method to validate
 * @returns {boolean} True if method is allowed
 */
function validateHttpMethod(method) {
  const security = global.config?.security;
  if (security?.enabled && security.allowedRestMethods) {
    return security.allowedRestMethods.includes(method);
  }
  return true;
}

/**
 * Checks if IP address is whitelisted
 * @param {string} ip - IP address to check
 * @returns {boolean} True if IP is whitelisted or no whitelist configured
 */
function validateIPWhitelist(ip) {
  const security = global.config?.security;
  if (security?.enabled && security.allowedIPs) {
    const ipRangeCheck = require("ip-range-check");
    const whitelist_ips = Object.values(security.allowedIPs);
    return ipRangeCheck(ip, whitelist_ips);
  }
  return true;
}

/**
 * Validates CORS origin against whitelist
 * @param {string} origin - Origin to validate
 * @returns {boolean} True if origin is allowed
 */
function validateCorsOrigin(origin) {
  const security = global.config?.security;
  if (security?.enabled && security.cors) {
    const whitelist_origins = Object.values(security.cors);
    return whitelist_origins.includes(origin);
  }
  return true;
}

/**
 * Creates default security headers object
 * @returns {Object} Security headers
 */
function createSecurityHeaders() {
  return {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET,POST",
    "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId",
    "X-FRAME-OPTIONS": "SAMEORIGIN",
    "X-XSS-Protection": "1;mode=block",
    "X-Content-Type-Options": "nosniff",
    "Content-Security-Policy": "script-src 'self'",
    "X-Permitted-Cross-Domain-Policies": "none",
    "Referrer-Policy": "no-referrer",
    "Strict-Transport-Security": "max-age=31536000 ; includeSubDomains"
  };
}

module.exports = {
  initializePlatform,
  initializeBasicConfig,
  initializeServices,
  checkTelnetConnection,
  setupHealthCheck,
  createLocaleSettings,
  createCpsDecoder,
  createPreProcessor,
  getClusterSize,
  createTlsOptions,
  setServerMode,
  setConfigurations,
  generateTxnId,
  validateHttpMethod,
  validateIPWhitelist,
  validateCorsOrigin,
  createSecurityHeaders,
  getAppManModuleFunc: () => require("./app_man.module"),
};
