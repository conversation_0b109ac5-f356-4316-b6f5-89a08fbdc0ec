{"name": "no-code-chatbot-backend", "version": "1.0.0", "description": "Production-ready no-code chatbot backend platform", "private": true, "workspaces": ["packages/*", "bot-builder-service", "bot-interaction-service", "chat-service", "chat-gateway", "training-server"], "scripts": {"install:all": "npm install && npm run install:services", "install:services": "npm install --workspaces", "build:all": "npm run build:shared && npm run build:services", "build:shared": "npm run build --workspace=@neuratalk/common && npm run build --workspace=@neuratalk/bot-store", "build:services": "npm run build --workspace=bot-interaction-service && npm run build --workspace=chat-service && npm run build --workspace=chat-gateway && npm run build --workspace=training-server", "dev:interaction": "npm run dev --workspace=bot-interaction-service", "dev:chat": "npm run dev --workspace=chat-service", "dev:gateway": "npm run dev --workspace=chat-gateway", "dev:training": "npm run dev --workspace=training-server", "start:interaction": "npm start --workspace=bot-interaction-service", "start:chat": "npm start --workspace=chat-service", "start:gateway": "npm start --workspace=chat-gateway", "start:training": "npm start --workspace=training-server", "test:all": "npm test --workspace=bot-interaction-service && npm test --workspace=chat-service && npm test --workspace=chat-gateway && npm test --workspace=training-server", "lint": "eslint . --ext .ts,.js --ignore-path .eslint<PERSON>ore", "lint:fix": "eslint . --ext .ts,.js --ignore-path .eslint<PERSON>ore --fix", "clean:all": "npm run clean --workspace=@neuratalk/common && npm run clean --workspace=bot-interaction-service && npm run clean --workspace=chat-service && npm run clean --workspace=chat-gateway && npm run clean --workspace=training-server", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "setup": "npm run install:all && npm run build:all", "health:check": "curl -f http://localhost:3000/health && curl -f http://localhost:3001/health && curl -f http://localhost:3002/health && curl -f http://localhost:5005/status", "format": "prettier . --write", "install:studio": "find studio -name 'package.json' -not -path '*/node_modules/*' -exec dirname {} \\; | xargs -I {} sh -c 'if [ $(basename {}) = 'utility' ]; then npm install -C {} --legacy-peer-deps; else npm install -C {}; fi'", "build:studio": "npm run build -C studio/api_gw && npm run build -C studio/app_engine"}, "keywords": ["chatbot", "no-code", "microservices", "typescript", "nodejs", "rasa", "postgresql", "redis", "rabbitmq"], "author": "Chatbot Platform Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-plugin-local-rules": "file:./eslint-local-rules", "prettier": "^3.6.2", "ts-json-schema-generator": "^2.4.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/no-code-chatbot-backend.git"}, "bugs": {"url": "https://github.com/your-org/no-code-chatbot-backend/issues"}, "homepage": "https://github.com/your-org/no-code-chatbot-backend#readme", "dependencies": {"axios": "^1.9.0", "jsonwebtoken": "^9.0.2", "utility": "^2.5.0"}}