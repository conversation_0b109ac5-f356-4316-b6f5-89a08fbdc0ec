# Docker Compose Configuration for No-Code Chatbot Platform
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: chatbot_postgres
    environment:
      POSTGRES_DB: chatbot_db
      POSTGRES_USER: chatbot_user
      POSTGRES_PASSWORD: chatbot_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./bot-builder-service/src/database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    networks:
      - chatbot_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chatbot_user -d chatbot_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: chatbot_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - chatbot_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Queue
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: chatbot_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: chatbot_user
      RABBITMQ_DEFAULT_PASS: chatbot_password
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - chatbot_network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Rasa NLU Service
  rasa-nlu:
    build:
      context: ./rasa-nlu
      dockerfile: Dockerfile
    container_name: chatbot_rasa_nlu
    ports:
      - "5005:5005"
    volumes:
      - ./rasa-nlu:/app
    networks:
      - chatbot_network
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5005/status"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Bot Builder Service
  bot-builder-service:
    build:
      context: ./bot-builder-service
      dockerfile: Dockerfile
    container_name: chatbot_bot_builder
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: ********************************************************/chatbot_db
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: chatbot_db
      DB_USER: chatbot_user
      DB_PASSWORD: chatbot_password
      BOT_INTERACTION_SERVICE_URL: http://bot-interaction-service:3001
      CHAT_SERVICE_URL: http://chat-service:3002
    ports:
      - "3000:3000"
    networks:
      - chatbot_network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Bot Interaction Service
  bot-interaction-service:
    build:
      context: ./bot-interaction-service
      dockerfile: Dockerfile
    container_name: chatbot_bot_interaction
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: ********************************************************/chatbot_db
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: chatbot_db
      DB_USER: chatbot_user
      DB_PASSWORD: chatbot_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: chatbot_user
      RABBITMQ_PASSWORD: chatbot_password
      RASA_NLU_URL: http://rasa-nlu:5005
      BOT_BUILDER_SERVICE_URL: http://bot-builder-service:3000
      CHAT_SERVICE_URL: http://chat-service:3002
      API_KEY: your-api-key-for-internal-services
    ports:
      - "3001:3001"
    networks:
      - chatbot_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      rasa-nlu:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Chat Service
  chat-service:
    build:
      context: ./chat-service
      dockerfile: Dockerfile
    container_name: chatbot_chat_service
    environment:
      NODE_ENV: production
      PORT: 3002
      DATABASE_URL: ********************************************************/chatbot_db
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: chatbot_db
      DB_USER: chatbot_user
      DB_PASSWORD: chatbot_password
      API_KEY: your-api-key-for-internal-services
    ports:
      - "3002:3002"
    networks:
      - chatbot_network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Chat Gateway Service
  chat-gateway:
    build:
      context: ./chat-gateway
      dockerfile: Dockerfile
    container_name: chatbot_chat_gateway
    environment:
      NODE_ENV: production
      PORT: 3003
      DATABASE_URL: ********************************************************/chatbot_db
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: chatbot_db
      DB_USER: chatbot_user
      DB_PASSWORD: chatbot_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      BOT_BUILDER_SERVICE_URL: http://bot-builder-service:3000
      BOT_INTERACTION_SERVICE_URL: http://bot-interaction-service:3001
      CHAT_SERVICE_URL: http://chat-service:3002
      AGENT_PORTAL_SERVICE_URL: http://agent-portal-service:3004
      API_KEY: your-api-key-for-internal-services
    ports:
      - "3003:3003"
    networks:
      - chatbot_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Rasa Server Service
  rasa-server:
    build:
      context: ./rasa-server
      dockerfile: Dockerfile
    container_name: chatbot_rasa_server
    environment:
      NODE_ENV: production
      PORT: 5005
      RASA_MODEL_PATH: /app/models
      RASA_TEMP_PATH: /tmp/rasa
      AWS_ACCESS_KEY_ID: your-access-key
      AWS_SECRET_ACCESS_KEY: your-secret-key
      S3_BUCKET: chatbot-models
      S3_ENDPOINT: http://minio:9000
      API_KEY: your-api-key-for-internal-services
    ports:
      - "5005:5005"
    volumes:
      - rasa_models:/app/models
    networks:
      - chatbot_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5005/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Training Server Service
  training-server:
    build:
      context: ./training-server
      dockerfile: Dockerfile
    container_name: chatbot_training_server
    environment:
      NODE_ENV: production
      PORT: 3005
      DATABASE_URL: ********************************************************/chatbot_db
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: chatbot_db
      DB_USER: chatbot_user
      DB_PASSWORD: chatbot_password
      AWS_ACCESS_KEY_ID: your-access-key
      AWS_SECRET_ACCESS_KEY: your-secret-key
      S3_BUCKET: chatbot-models
      S3_ENDPOINT: http://minio:9000
      TRAINING_TEMP_PATH: /tmp/training
      API_KEY: your-api-key-for-internal-services
    ports:
      - "3005:3005"
    volumes:
      - training_temp:/tmp/training
    networks:
      - chatbot_network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO for S3-compatible storage
  minio:
    image: minio/minio:latest
    container_name: chatbot_minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - chatbot_network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  rasa_models:
  training_temp:
  minio_data:

networks:
  chatbot_network:
    driver: bridge
