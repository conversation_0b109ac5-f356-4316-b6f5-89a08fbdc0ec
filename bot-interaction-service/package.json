{"name": "@chatbot/bot-interaction-service", "version": "1.0.0", "description": "Core conversation engine for the no-code chatbot platform", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "migrate:create": "sequelize-cli migration:generate --name", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "test:coverage": "jest --coverage", "security:audit": "npm audit --audit-level moderate", "docker:build": "docker build -t bot-interaction-service .", "docker:run": "docker run -p 3001:3001 bot-interaction-service"}, "keywords": ["chatbot", "conversation", "engine", "typescript"], "author": "Chatbot Platform Team", "license": "MIT", "dependencies": {"@neuratalk/bot-store": "file:../packages/bot-store", "@neuratalk/common": "file:../packages/common", "@studio/app-engine": "file:../studio/app_engine", "axios": "^1.4.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.1.4", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "handlebars": "^4.7.7", "helmet": "^7.0.0", "isomorphic-dompurify": "^2.6.0", "zod": "^3.22.4", "morgan": "^1.10.0", "mysql2": "^3.6.0", "redis": "^4.6.7", "sequelize": "^6.32.1", "sequelize-cli": "^6.6.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.0", "winston": "^3.9.0"}, "devDependencies": {"@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/morgan": "^1.9.4", "@types/node": "^20.3.1", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^8.43.0", "jest": "^29.5.0", "supertest": "^6.3.3", "@types/supertest": "^2.0.12", "ts-jest": "^29.1.0", "ts-node-dev": "^2.0.0", "typescript": "^5.2.0", "@asteasolutions/zod-to-openapi": "^7.3.4"}, "engines": {"node": ">=18.0.0"}}