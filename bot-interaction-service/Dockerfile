# Multi-stage build for production optimization
FROM node:18-alpine AS builder

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Copy local dependencies and their package.json files
COPY packages/bot-store/package*.json ./packages/bot-store/
COPY packages/common/package*.json ./packages/common/
COPY studio/app_engine/package*.json ./studio/app_engine/

# Install all dependencies (including dev)
RUN npm ci

# Copy source code for local dependencies and build them
COPY packages/bot-store/ ./packages/bot-store/
RUN cd ./packages/bot-store && npm install && npm run build

COPY packages/common/ ./packages/common/
RUN cd ./packages/common && npm install && npm run build

COPY studio/app_engine/ ./studio/app_engine/
RUN cd ./studio/app_engine && npm install && npm run build

# Copy source code for the main application
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl && \
    rm -rf /var/cache/apk/*

# Create non-root user with specific UID/GID
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# Copy built application from builder stage
COPY --from=builder --chown=nodejs:nodejs /app/dist ./dist

# Copy built local dependencies from builder stage
COPY --from=builder --chown=nodejs:nodejs /app/packages/bot-store/dist ./packages/bot-store/dist
COPY --from=builder --chown=nodejs:nodejs /app/packages/common/dist ./packages/common/dist
COPY --from=builder --chown=nodejs:nodejs /app/studio/app_engine/dist ./studio/app_engine/dist

# Copy package.json for local dependencies (needed for module resolution)
COPY --from=builder --chown=nodejs:nodejs /app/packages/bot-store/package.json ./packages/bot-store/package.json
COPY --from=builder --chown=nodejs:nodejs /app/packages/common/package.json ./packages/common/package.json
COPY --from=builder --chown=nodejs:nodejs /app/studio/app_engine/package.json ./studio/app_engine/package.json

# Create logs directory
RUN mkdir -p logs && chown nodejs:nodejs logs

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3001

# Health check with proper timeout and retries
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3001/health/live || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/index.js"]
