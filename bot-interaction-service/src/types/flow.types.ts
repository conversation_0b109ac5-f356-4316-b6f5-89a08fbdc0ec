/**
 * Flow and Node Type Definitions
 *
 * These types define the structure of conversational flows and their constituent nodes.
 * The flow execution model is based on condition-based routing where each node contains
 * its own routing logic through a conditions array.
 */

// --- Core Flow Structures ---

export interface Flow {
  id: string;
  name: string;
  description?: string;
  botId: string;
  version: number;
  isActive: boolean;
  entryNodeId: string; // The starting node for this flow
  nodes: Record<string, Node>;
  createdAt: Date;
  updatedAt: Date;
}

export interface Condition {
  variable?: string; // e.g., "context.orderStatus", "request.status", "user.input"
  value?: any; // e.g., "shipped", "success", 200
  type:
    | "equal"
    | "not_equal"
    | "is_defined"
    | "is_undefined"
    | "greater_than"
    | "less_than"
    | "contains"
    | "starts_with"
    | "ends_with"
    | "regex_match";
  than: string; // The nodeId to go to next if condition is true
}

// --- Node Type Definitions ---

export type NodeType =
  | "intent"
  | "message"
  | "form"
  | "request"
  | "script"
  | "flow_connector";

export interface BaseNode {
  id: string;
  type: NodeType;
  name?: string;
  description?: string;
  condition: Condition[]; // Routing conditions - last should be default/fallback
}

/**
 * Intent Node - Entry point for flows
 * Matches user intent and routes to appropriate next node
 */
export interface IntentNode extends BaseNode {
  type: "intent";
  intent: string; // The intent name to match (from NLU)
  confidence?: number; // Minimum confidence threshold (0-1)
  examples: string[];
  entities?: string[]; //TODO: need to update it's type
}

/**
 * Message Node - Sends templated messages to users
 * Uses Handlebars templating with context data
 */
export interface MessageNode extends BaseNode {
  type: "message";
  template: string; // Handlebars template string
  delay?: number; // Optional delay in milliseconds before sending
}

/**
 * Form Node - Collects user input
 * Implements ask/collect pattern with validation
 */
export interface FormNode extends BaseNode {
  type: "form";
  field: {
    label: string; // Question to ask the user
    contextVariable: string; // Where to store the collected value
    validation?: {
      type: "text" | "number" | "email" | "phone" | "regex";
      pattern?: string; // For regex validation
      min?: number; // For number validation
      max?: number; // For number validation
      required?: boolean;
    };
    retryMessage?: string; // Message when validation fails
    maxRetries?: number; // Maximum retry attempts
  };
}

/**
 * Request Node - Makes HTTP requests asynchronously
 * Publishes to message queue and pauses flow execution
 */
export interface RequestNode extends BaseNode {
  type: "request";
  url: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: any; // Request body (for POST/PUT/PATCH)
  timeout?: number; // Request timeout in milliseconds
  responseMapping: Record<string, string>; // Maps response fields to context variables
  waitingMessage?: string; // Message to show while waiting for response
}

/**
 * Script Node - Executes JavaScript in sandboxed environment
 * Runs in worker thread to avoid blocking main event loop
 */
export interface ScriptNode extends BaseNode {
  type: "script";
  script: string; // JavaScript code to execute
  timeout?: number; // Execution timeout in milliseconds (default: 100ms)
  allowedModules?: string[]; // Whitelisted modules that can be imported
}

/**
 * Flow Connector Node - Transitions between flows
 * Terminates current flow and starts target flow
 */
export interface FlowConnectorNode extends BaseNode {
  type: "flow_connector";
  targetFlowId: string;
  preserveContext?: boolean; // Whether to maintain context across flows
}

// Union type for all node types
export type Node =
  | IntentNode
  | MessageNode
  | FormNode
  | RequestNode
  | ScriptNode
  | FlowConnectorNode;

// --- Flow Execution Types ---

export interface FlowExecution {
  id: string;
  flowId: string;
  conversationId: string;
  currentNodeId: string | null;
  status: "running" | "paused" | "completed" | "failed";
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}

export interface NodeExecution {
  id: string;
  flowExecutionId: string;
  nodeId: string;
  nodeType: NodeType;
  status: "pending" | "running" | "completed" | "failed" | "skipped";
  startedAt: Date;
  completedAt?: Date;
  input?: any;
  output?: any;
  error?: string;
  retryCount?: number;
}