/**
 * Redis Service
 *
 * Handles Redis connections and operations for session management and caching.
 * Implements cache-aside pattern for bot flows and conversation contexts.
 */

import { createClient, RedisClientType } from "redis";
import config from "../config";
import { Flow, IntentItems } from "@neuratalk/bot-store";
import { ConversationContext } from "../types";
import { logger } from "@neuratalk/common";

export class RedisService {
  private client: RedisClientType;
  private isConnected: boolean = false;

  constructor() {
    this.client = createClient({
      url: config.redis.url,
      socket: {
        host: config.redis.host,
        port: config.redis.port,
      },
      password: config.redis.password,
      database: config.redis.db,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.client.on("connect", () => {
      logger.info("Redis client connected");
      this.isConnected = true;
    });

    this.client.on("error", (err) => {
      logger.error("Redis client error:", err);
      this.isConnected = false;
    });

    this.client.on("end", () => {
      logger.info("Redis client disconnected");
      this.isConnected = false;
    });
  }

  async connect(): Promise<void> {
    try {
      await this.client.connect();
      logger.info("Redis service initialized successfully");
    } catch (error) {
      logger.error("Failed to connect to Redis:", error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.client.quit();
      logger.info("Redis service disconnected");
    } catch (error) {
      logger.error("Error disconnecting from Redis:", error);
    }
  }

  private getKey(key: string): string {
    return `${config.redis.keyPrefix}${key}`;
  }

  // --- Conversation Context Management ---

  async getConversationContext(conversationId: string): Promise<ConversationContext | null> {
    try {
      const key = this.getKey(`context:${conversationId}`);
      const data = await this.client.get(key);

      if (!data) {
        return null;
      }

      const context = JSON.parse(data) as ConversationContext;

      // Convert date strings back to Date objects
      context.sessionStartedAt = new Date(context.sessionStartedAt);
      context.lastActivityAt = new Date(context.lastActivityAt);
      context.expiresAt = new Date(context.expiresAt);

      return context;
    } catch (error) {
      logger.error(`Error getting conversation context for ${conversationId}:`, error);
      return null;
    }
  }

  async setConversationContext(
    context: ConversationContext,
    ttlMinutes?: number,
  ): Promise<boolean> {
    try {
      const key = this.getKey(`context:${context.chatConversationId}`);
      const ttl = ttlMinutes || config.session.ttlMinutes;

      // Update last activity and expiry
      context.lastActivityAt = new Date();
      context.expiresAt = new Date(Date.now() + ttl * 60 * 1000);

      await this.client.setEx(key, ttl * 60, JSON.stringify(context));

      logger.debug(
        `Conversation context saved for ${context.chatConversationId} with TTL ${ttl} minutes`,
      );
      return true;
    } catch (error) {
      logger.error(`Error setting conversation context for ${context.chatConversationId}:`, error);
      return false;
    }
  }

  async updateConversationContext(
    conversationId: string,
    updates: Partial<ConversationContext>,
  ): Promise<boolean> {
    try {
      const existingContext = await this.getConversationContext(conversationId);

      if (!existingContext) {
        logger.warn(`Attempted to update non-existent context: ${conversationId}`);
        return false;
      }

      const updatedContext = { ...existingContext, ...updates };
      return await this.setConversationContext(updatedContext);
    } catch (error) {
      logger.error(`Error updating conversation context for ${conversationId}:`, error);
      return false;
    }
  }

  async deleteConversationContext(conversationId: string): Promise<boolean> {
    try {
      const key = this.getKey(`context:${conversationId}`);
      const result = await this.client.del(key);

      logger.debug(`Conversation context deleted for ${conversationId}`);
      return result > 0;
    } catch (error) {
      logger.error(`Error deleting conversation context for ${conversationId}:`, error);
      return false;
    }
  }

  // --- Flow Caching (Cache-Aside Pattern) ---

  async getFlow(flowId: string): Promise<Flow | null> {
    try {
      const key = this.getKey(`flow:${flowId}`);
      const data = await this.client.get(key);

      if (!data) {
        return null;
      }

      return JSON.parse(data);
    } catch (error) {
      logger.error(`Error getting cached flow ${flowId}:`, error);
      return null;
    }
  }

  async setFlow(flowId: string, flow: any, ttlMinutes: number = 60): Promise<boolean> {
    try {
      const key = this.getKey(`flow:${flowId}`);
      await this.client.setEx(key, ttlMinutes * 60, JSON.stringify(flow));

      logger.debug(`Flow ${flowId} cached with TTL ${ttlMinutes} minutes`);
      return true;
    } catch (error) {
      logger.error(`Error caching flow ${flowId}:`, error);
      return false;
    }
  }

  async invalidateFlow(flowId: string): Promise<boolean> {
    try {
      const key = this.getKey(`flow:${flowId}`);
      const result = await this.client.del(key);

      logger.debug(`Flow cache invalidated for ${flowId}`);
      return result > 0;
    } catch (error) {
      logger.error(`Error invalidating flow cache for ${flowId}:`, error);
      return false;
    }
  }

  // --- General Cache Operations ---

  async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(this.getKey(key));
    } catch (error) {
      logger.error(`Error getting key ${key}:`, error);
      return null;
    }
  }

  async set(key: string, value: string, ttlSeconds?: number): Promise<boolean> {
    try {
      if (ttlSeconds) {
        await this.client.setEx(this.getKey(key), ttlSeconds, value);
      } else {
        await this.client.set(this.getKey(key), value);
      }
      return true;
    } catch (error) {
      logger.error(`Error setting key ${key}:`, error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      const result = await this.client.del(this.getKey(key));
      return result > 0;
    } catch (error) {
      logger.error(`Error deleting key ${key}:`, error);
      return false;
    }
  }

  // --- Health Check ---

  async healthCheck(): Promise<{
    status: "healthy" | "unhealthy";
    responseTime?: number;
  }> {
    const start = Date.now();

    try {
      await this.client.ping();
      const responseTime = Date.now() - start;

      return {
        status: "healthy",
        responseTime,
      };
    } catch (error) {
      logger.error("Redis health check failed:", error);
      return {
        status: "unhealthy",
      };
    }
  }

  get connected(): boolean {
    return this.isConnected;
  }
}
