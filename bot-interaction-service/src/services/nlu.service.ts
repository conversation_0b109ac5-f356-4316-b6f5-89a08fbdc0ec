/**
 * NLU Service
 *
 * Handles communication with Rasa NLU for intent recognition and entity extraction.
 * Provides fallback mechanisms and confidence threshold handling.
 */

import axios, { AxiosInstance } from "axios";
import config from "../config";
import { NLURequest, NLUResponse } from "../types";
import { logger } from "@neuratalk/common";

export class NLUService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.services.rasaNluUrl,
      timeout: 5000,
      headers: {
        "Content-Type": "application/json",
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`NLU Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error("NLU Request error:", error);
        return Promise.reject(error);
      },
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`NLU Response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        logger.error("NLU Response error:", error);
        return Promise.reject(error);
      },
    );
  }

  /**
   * Parse user message to extract intent and entities
   */
  async parseMessage(request: NLURequest): Promise<NLUResponse> {
    try {
      const response = await this.client.post("/model/parse", {
        text: request.text,
        message_id: request.conversationId,
      });

      const data = response.data;

      // Extract intent with confidence
      const intent = {
        name: data.intent?.name || "nlu_fallback",
        confidence: data.intent?.confidence || 0,
      };

      // Extract entities
      const entities = (data.entities || []).map((entity: any) => ({
        name: entity.entity,
        value: entity.value,
        confidence: entity.confidence || 1,
        start: entity.start,
        end: entity.end,
      }));

      logger.debug(`NLU parsed "${request.text}" -> Intent: ${intent.name} (${intent.confidence})`);

      return {
        intent,
        entities,
        text: request.text,
        conversationId: request.conversationId,
      };
    } catch (error) {
      logger.error("Error parsing message with NLU:", error);

      // Return fallback response
      return {
        intent: {
          name: "nlu_fallback",
          confidence: 0,
        },
        entities: [],
        text: request.text,
        conversationId: request.conversationId,
      };
    }
  }

  /**
   * Check if intent confidence meets threshold
   */
  isIntentConfident(
    intent: { name: string; confidence: number },
    threshold: number = 0.7,
  ): boolean {
    return intent.confidence >= threshold && intent.name !== "nlu_fallback";
  }

  /**
   * Get fallback intent for low confidence scenarios
   */
  getFallbackIntent(): { name: string; confidence: number } {
    return {
      name: "nlu_fallback",
      confidence: 0,
    };
  }

  /**
   * Train the NLU model (if supported)
   */
  async trainModel(trainingData: any): Promise<boolean> {
    try {
      const response = await this.client.post("/model/train", trainingData);

      if (response.status === 200) {
        logger.info("NLU model training initiated successfully");
        return true;
      }

      return false;
    } catch (error) {
      logger.error("Error training NLU model:", error);
      return false;
    }
  }

  /**
   * Get model status and information
   */
  async getModelStatus(): Promise<any> {
    try {
      const response = await this.client.get("/status");
      return response.data;
    } catch (error) {
      logger.error("Error getting NLU model status:", error);
      return null;
    }
  }

  /**
   * Health check for NLU service
   */
  async healthCheck(): Promise<{
    status: "healthy" | "unhealthy";
    responseTime?: number;
    error?: string;
  }> {
    const start = Date.now();

    try {
      const response = await this.client.get("/status");
      const responseTime = Date.now() - start;

      if (response.status === 200) {
        return {
          status: "healthy",
          responseTime,
        };
      } else {
        return {
          status: "unhealthy",
          error: `Unexpected status code: ${response.status}`,
        };
      }
    } catch (error) {
      const responseTime = Date.now() - start;
      logger.error("NLU health check failed:", error);

      return {
        status: "unhealthy",
        responseTime,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Test NLU with a simple message
   */
  async testConnection(): Promise<boolean> {
    try {
      const testRequest: NLURequest = {
        text: "hello",
        conversationId: "test",
      };

      const response = await this.parseMessage(testRequest);
      return response.intent.name !== undefined;
    } catch (error) {
      logger.error("NLU connection test failed:", error);
      return false;
    }
  }
}
