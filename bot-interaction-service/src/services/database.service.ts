import { logger } from "@neuratalk/common";
import { Bot, DatabaseConnection, Flow, IntentItems, FaqItems, Models } from "@neuratalk/bot-store";

export class DatabaseService {
  private models: Models;
  constructor(private db: DatabaseConnection) {
    this.models = this.db.models;
  }

  // --- Flow Operations (Read-Only) ---

  getFlow = async (flowId: string): Promise<Flow | null> => {
    try {
      const flow = await this.models.Flow.findByPk(flowId);

      if (!flow) {
        logger.warn(`Flow with ID: ${flowId} not found or is inactive.`);
        return null;
      }

      return flow;
    } catch (error) {
      logger.error(`Error fetching flow ${flowId}:`, error);
      return null;
    }
  };

  async getFlowsByBot(botId: string): Promise<Flow[]> {
    try {
      const flows = await this.models.Flow.findAll({
        where: {
          botId: botId,
        },
        order: [["createdAt", "DESC"]],
      });

      return flows;
    } catch (error) {
      logger.error(`Error fetching flows for bot ${botId}:`, error);
      return [];
    }
  }

  async getIntentItemByName(botId: string, name: string): Promise<IntentItems | null> {
    try {
      const intentItem = await this.models.IntentItems.findOne({
        where: {
          botId: botId,
          name: name,
        },
      });
      return intentItem;
    } catch (error) {
      logger.error(`Error fetching intent item by name ${name} for bot ${botId}:`, error);
      return null;
    }
  }

  async getIntentItemWithFlow(intentItemId: string): Promise<IntentItems | null> {
    try {
      const intentItem = await this.models.IntentItems.findByPk(intentItemId, {
        include: [{ model: this.models.Flow, as: "flow" }],
      });
      return intentItem;
    } catch (error) {
      logger.error(`Error fetching intent item ${intentItemId} with flow:`, error);
      return null;
    }
  }

  async getFaqItemWithFlow(faqItemId: string): Promise<FaqItems | null> {
    try {
      const faqItem = await this.models.FaqItems.findByPk(faqItemId, {
        include: [{ model: this.models.Flow, as: "flow" }],
      });
      return faqItem;
    } catch (error) {
      logger.error(`Error fetching FAQ item ${faqItemId} with flow:`, error);
      return null;
    }
  }

  // --- Bot Operations (Read-Only) ---

  async getBot(botId: string): Promise<Bot | null> {
    try {
      const bot = await this.models.Bot.findOne({
        where: {
          id: botId,
          status: "active",
        },
      });

      if (!bot) {
        logger.warn(`Bot with ID: ${botId} not found or is inactive.`);
        return null;
      }

      return bot;
    } catch (error) {
      logger.error(`Error fetching bot ${botId}:`, error);
      return null;
    }
  }
}
