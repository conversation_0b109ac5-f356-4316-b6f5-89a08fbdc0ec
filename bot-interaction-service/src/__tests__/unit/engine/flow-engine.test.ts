/**
 * Flow Engine Unit Tests
 */

import { FlowEngine } from "../../../engine/flow-engine";
import { RedisService } from "../../../services/redis.service";
import { DatabaseService } from "../../../services/database.service";
import { NLUService } from "../../../services/nlu.service";

// Mock services
jest.mock("../../../services/redis.service");
jest.mock("../../../services/database.service");
jest.mock("../../../services/nlu.service");

describe("FlowEngine", () => {
  let flowEngine: FlowEngine;
  let mockRedisService: jest.Mocked<RedisService>;
  let mockDatabaseService: jest.Mocked<DatabaseService>;
  let mockNLUService: jest.Mocked<NLUService>;

  beforeEach(() => {
    mockRedisService = new RedisService() as jest.Mocked<RedisService>;
    mockDatabaseService = new DatabaseService() as jest.Mocked<DatabaseService>;
    mockNLUService = new NLUService() as jest.Mocked<NLUService>;

    flowEngine = new FlowEngine(
      mockRedisService,
      mockDatabaseService,
      mockNLUService
    );
  });

  describe("processMessage", () => {
    it("should handle errors gracefully", async () => {
      const conversationId = "test-conversation-id";
      const botId = "test-bot-id";
      const userMessage = "Hello";

      mockRedisService.getConversationContext.mockRejectedValue(
        new Error("Redis error")
      );

      const result = await flowEngine.processMessage(
        conversationId,
        userMessage,
        botId
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe("Redis error");
      expect(result.messages).toHaveLength(1);
      expect(result.messages[0].content).toContain("encountered an error");
    });
  });

  describe("resumeFlow", () => {
    it("should handle missing context error", async () => {
      const conversationId = "test-conversation-id";
      const nodeId = "test-node-id";
      const responseData = { status: "success" };

      mockRedisService.getConversationContext.mockResolvedValue(null);

      const result = await flowEngine.resumeFlow(
        conversationId,
        nodeId,
        responseData,
        true
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain("Context not found");
    });
  });
});
