import express, { Application, Request, Response, NextFunction } from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import morgan from "morgan";
import swaggerUi from "swagger-ui-express";
import swaggerSpec from "./config/swagger";

// Services
import { RedisService } from "./services/redis.service";
import { NLUService } from "./services/nlu.service";

// Utils
import config from "./config";
import { ApiResponse, logger } from "@neuratalk/common";
import { DatabaseConnection } from "@neuratalk/bot-store";
import { AppContext } from "./types/context.types";
import { createRoutes } from "./routes/index.router";

export class App {
  public app: Application;
  private context!: AppContext;

  constructor() {
    this.app = express();
  }

  private async initializeCoreServices(): Promise<void> {
    const databaseConnection = new DatabaseConnection();
    await databaseConnection.connect();

    const redisService = new RedisService();
    await redisService.connect();

    const nluService = new NLUService();
    // Test NLU connection (non-blocking)
    nluService.testConnection().then((connected) => {
      if (connected) {
        logger.info("NLU service connection verified");
      } else {
        logger.warn("NLU service connection failed - will retry on demand");
      }
    });

    this.context = {
      db: databaseConnection,
      redis: redisService,
      nlu: nluService,
    };

    logger.info("Core services initialized.");
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(
      helmet({
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
          },
        },
      }),
    );

    // CORS configuration
    this.app.use(
      cors({
        origin: config.server.corsOrigins,
        credentials: true,
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allowedHeaders: ["Content-Type", "Authorization", "X-Bot-Id", "X-API-Key"],
      }),
    );

    // Compression
    this.app.use(compression());

    // Request parsing
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // Logging
    if (config.server.env !== "test") {
      this.app.use(
        morgan("combined", {
          stream: {
            write: (message: string) => {
              logger.info(message.trim());
            },
          },
        }),
      );
    }

    // Request ID middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const requestId =
        (req.headers["x-request-id"] as string) ||
        `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      (req as any).requestId = requestId;
      res.setHeader("X-Request-ID", requestId);
      next();
    });

    // Request logging
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const start = Date.now();

      res.on("finish", () => {
        const duration = Date.now() - start;
        logger.info(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`, {
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
          duration,
          requestId: (req as any).requestId,
          userAgent: req.get("User-Agent"),
          ip: req.ip,
        });
      });

      next();
    });
  }

  private initializeRoutes(): void {
    this.app.use("/api/v1", ...createRoutes(this.context));

    // Swagger documentation
    this.app.use(
      "/api-docs",
      swaggerUi.serve,
      swaggerUi.setup(swaggerSpec, {
        explorer: true,
        swaggerOptions: {
          persistAuthorization: true,
          docExpansion: "list",
        },
      }),
    );

    // Swagger JSON endpoint
    this.app.get("/api-docs.json", (req: Request, res: Response) => {
      res.setHeader("Content-Type", "application/json");
      res.send(swaggerSpec);
    });

    // Root endpoint
    this.app.get("/", (req: Request, res: Response) => {
      res.json({
        success: true,
        data: {
          service: "bot-interaction-service",
          version: process.env.npm_package_version || "1.0.0",
          environment: config.server.env,
          timestamp: new Date(),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });

    // 404 handler
    this.app.use("*", (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: `Route ${req.method} ${req.originalUrl} not found`,
        },
        timestamp: new Date(),
      } as ApiResponse);
    });
  }

  private initializeErrorHandling(): void {
    // Global error handler
    this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      logger.error("Unhandled error:", {
        error: error.message,
        stack: error.stack,
        requestId: (req as any).requestId,
        method: req.method,
        path: req.path,
        body: req.body,
      });

      // Don't expose internal errors in production
      const message =
        config.server.env === "production" ? "An internal error occurred" : error.message;

      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message,
          ...(config.server.env !== "production" && { stack: error.stack }),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });

    // Handle uncaught exceptions
    process.on("uncaughtException", (error: Error) => {
      logger.error("Uncaught Exception:", error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on("unhandledRejection", (reason: any, promise: Promise<any>) => {
      logger.error("Unhandled Rejection at:", promise, "reason:", reason);
      process.exit(1);
    });
  }

  public async start(): Promise<void> {
    try {
      await this.initializeCoreServices();
      this.initializeMiddleware();
      this.initializeRoutes();
      this.initializeErrorHandling();

      // Start server
      const port = config.server.port;
      this.app.listen(port, () => {
        logger.info(`Bot Interaction Service started on port ${port}`);
        logger.info(`Environment: ${config.server.env}`);
        logger.info(`Health check: http://localhost:${port}/health`);
      });
    } catch (error) {
      logger.error("Failed to start application:", error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      logger.info("Shutting down Bot Interaction Service...");

      await this.context.redis.disconnect();
      await this.context.db.disconnect();

      logger.info("Bot Interaction Service stopped");
    } catch (error) {
      logger.error("Error during shutdown:", error);
    }
  }
}
