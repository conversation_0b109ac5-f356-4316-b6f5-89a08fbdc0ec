/**
 * Swagger Configuration for Bot Interaction Service
 *
 * OpenAPI 3.0 specification for conversation and interaction APIs
 */

import swaggerJsdoc from "swagger-jsdoc";
import { SwaggerDefinition } from "swagger-jsdoc";

const swaggerDefinition: SwaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "Bot Interaction Service API",
    version: "1.0.0",
    description:
      "Core conversation engine API for processing user messages and managing chat sessions",
    contact: {
      name: "Chatbot Platform Team",
      email: "<EMAIL>",
    },
    license: {
      name: "MIT",
      url: "https://opensource.org/licenses/MIT",
    },
  },
  servers: [
    {
      url: "http://localhost:3001",
      description: "Development server",
    },
    {
      url: "https://chat.chatbot-platform.com",
      description: "Production server",
    },
  ],
  components: {
    securitySchemes: {
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "X-API-Key",
        description: "API key for internal service authentication",
      },
      BotIdAuth: {
        type: "apiKey",
        in: "header",
        name: "X-Bot-Id",
        description: "Bot identifier for conversation context",
      },
    },
    schemas: {
      SendMessageRequest: {
        type: "object",
        required: ["content"],
        properties: {
          content: {
            type: "string",
            description: "Message content from user",
            example: "Hello, I need help with my order",
            minLength: 1,
            maxLength: 4000,
          },
          messageType: {
            type: "string",
            enum: ["text", "image", "file"],
            description: "Type of message",
            default: "text",
            example: "text",
          },
          metadata: {
            type: "object",
            description: "Additional message metadata",
            additionalProperties: true,
            example: {
              userId: "user123",
              channel: "web",
            },
          },
        },
      },
      OutgoingMessage: {
        type: "object",
        required: ["conversationId", "content", "messageType"],
        properties: {
          conversationId: {
            type: "string",
            format: "uuid",
            description: "Conversation identifier",
          },
          content: {
            type: "string",
            description: "Bot response content",
            example: "Hello! How can I help you today?",
          },
          messageType: {
            type: "string",
            enum: ["text", "image", "file", "quick_reply", "card", "carousel"],
            description: "Type of bot response",
            example: "text",
          },
          delay: {
            type: "integer",
            description: "Delay before sending message (milliseconds)",
            example: 1000,
          },
          metadata: {
            type: "object",
            description: "Additional response metadata",
            additionalProperties: true,
          },
        },
      },
      SendMessageResponse: {
        type: "object",
        required: ["messageId", "response", "conversationState"],
        properties: {
          messageId: {
            type: "string",
            format: "uuid",
            description: "Unique message identifier",
          },
          response: {
            $ref: "#/components/schemas/OutgoingMessage",
            description: "Bot response message",
          },
          conversationState: {
            type: "string",
            enum: ["active", "waiting_input", "async_operation", "completed"],
            description: "Current conversation state",
            example: "active",
          },
        },
      },
      ConversationContext: {
        type: "object",
        required: ["chatConversationId", "botId"],
        properties: {
          chatConversationId: {
            type: "string",
            format: "uuid",
            description: "Conversation session ID",
          },
          botId: {
            type: "string",
            format: "uuid",
            description: "Associated bot ID",
          },
          currentFlowId: {
            type: "string",
            description: "Currently executing flow ID",
          },
          currentNodeId: {
            type: "string",
            description: "Current node in flow execution",
          },
          waitingForInput: {
            type: "boolean",
            description: "Whether bot is waiting for user input",
            example: false,
          },
          asyncOperationInProgress: {
            type: "boolean",
            description: "Whether an async operation is running",
            example: false,
          },
          sessionStartedAt: {
            type: "string",
            format: "date-time",
            description: "Session start timestamp",
          },
          lastActivityAt: {
            type: "string",
            format: "date-time",
            description: "Last activity timestamp",
          },
          expiresAt: {
            type: "string",
            format: "date-time",
            description: "Session expiration timestamp",
          },
        },
        additionalProperties: true,
      },
      ResumeFlowRequest: {
        type: "object",
        required: ["conversationId", "nodeId", "success"],
        properties: {
          conversationId: {
            type: "string",
            format: "uuid",
            description: "Conversation identifier",
          },
          nodeId: {
            type: "string",
            description: "Node ID to resume from",
          },
          responseData: {
            type: "object",
            description: "Data from async operation",
            additionalProperties: true,
          },
          success: {
            type: "boolean",
            description: "Whether async operation succeeded",
            example: true,
          },
          error: {
            type: "string",
            description: "Error message if operation failed",
          },
        },
      },
      ResumeFlowResponse: {
        type: "object",
        required: ["success", "conversationState"],
        properties: {
          success: {
            type: "boolean",
            description: "Whether flow resumption succeeded",
          },
          nextMessage: {
            $ref: "#/components/schemas/OutgoingMessage",
            description: "Next message in flow (if any)",
          },
          conversationState: {
            type: "string",
            enum: ["active", "completed", "failed"],
            description: "Updated conversation state",
          },
        },
      },
      HealthCheckResponse: {
        type: "object",
        required: ["status", "timestamp", "version", "uptime"],
        properties: {
          status: {
            type: "string",
            enum: ["healthy", "degraded", "unhealthy"],
            description: "Overall service health status",
            example: "healthy",
          },
          timestamp: {
            type: "string",
            format: "date-time",
            description: "Health check timestamp",
          },
          version: {
            type: "string",
            description: "Service version",
            example: "1.0.0",
          },
          uptime: {
            type: "integer",
            description: "Service uptime in milliseconds",
            example: 3600000,
          },
          dependencies: {
            type: "array",
            description: "Status of external dependencies",
            items: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  description: "Dependency name",
                  example: "redis",
                },
                status: {
                  type: "string",
                  enum: ["healthy", "unhealthy"],
                  description: "Dependency status",
                  example: "healthy",
                },
                responseTime: {
                  type: "integer",
                  description: "Response time in milliseconds",
                  example: 5,
                },
                error: {
                  type: "string",
                  description: "Error message if unhealthy",
                },
              },
            },
          },
        },
      },
      ApiResponse: {
        type: "object",
        required: ["success", "timestamp"],
        properties: {
          success: {
            type: "boolean",
            description: "Whether the request was successful",
          },
          data: {
            description: "Response data (present on success)",
          },
          error: {
            type: "object",
            description: "Error details (present on failure)",
            properties: {
              code: {
                type: "string",
                description: "Error code",
              },
              message: {
                type: "string",
                description: "Error message",
              },
              details: {
                description: "Additional error details",
              },
            },
          },
          timestamp: {
            type: "string",
            format: "date-time",
            description: "Response timestamp",
          },
        },
      },
    },
  },
  tags: [
    {
      name: "Conversations",
      description: "Conversation management and message processing",
    },
    {
      name: "Internal",
      description: "Internal service endpoints",
    },
    {
      name: "Health",
      description: "Health check and monitoring endpoints",
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: ["./src/controllers/*.ts", "./src/routes/*.ts"],
};

export const swaggerSpec = swaggerJsdoc(options);
export default swaggerSpec;
