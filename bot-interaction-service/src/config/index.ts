/**
 * Configuration Management
 *
 * Centralized configuration for the bot-interaction-service.
 * Uses environment variables with sensible defaults.
 */

import dotenv from "dotenv";

// Load environment variables
dotenv.config();

export interface Config {
  server: {
    port: number;
    env: string;
    corsOrigins: string[];
  };
  database: {
    url: string;
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
    ssl: boolean;
    maxConnections: number;
  };
  redis: {
    url: string;
    host: string;
    port: number;
    password?: string;
    db: number;
    keyPrefix: string;
  };
  services: {
    rasaNluUrl: string;
    botBuilderUrl: string;
    chatServiceUrl: string;
  };
  security: {
    jwtSecret: string;
    apiKey: string;
  };
  session: {
    ttlMinutes: number;
    maxConcurrentSessions: number;
    scriptTimeoutMs: number;
    asyncTimeoutMs: number;
  };
  logging: {
    level: string;
    file?: string;
  };
}

const config: Config = {
  server: {
    port: parseInt(process.env.PORT || "3001", 10),
    env: process.env.NODE_ENV || "development",
    corsOrigins: process.env.CORS_ORIGINS?.split(",") || [
      "http://localhost:3000",
      "http://localhost:8080",
    ],
  },
  database: {
    url:
      process.env.DB_URL ||
      "postgresql://username:password@localhost:5432/chatbot_db",
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT || "5432", 10),
    name: process.env.DB_NAME || "chatbot_db",
    user: process.env.DB_USER || "username",
    password: process.env.DB_PASSWORD || "password",
    ssl: process.env.DB_SSL === "true",
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || "20", 10),
  },
  redis: {
    url: process.env.REDIS_URL || "redis://localhost:6379",
    host: process.env.REDIS_HOST || "localhost",
    port: parseInt(process.env.REDIS_PORT || "6379", 10),
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || "0", 10),
    keyPrefix: "chatbot:",
  },
  services: {
    rasaNluUrl: process.env.RASA_NLU_URL || "http://localhost:5005",
    botBuilderUrl:
      process.env.BOT_BUILDER_SERVICE_URL || "http://localhost:3000",
    chatServiceUrl: process.env.CHAT_SERVICE_URL || "http://localhost:3002",
  },
  security: {
    jwtSecret: process.env.JWT_SECRET || "your-super-secret-jwt-key",
    apiKey: process.env.API_KEY || "your-api-key-for-internal-services",
  },
  session: {
    ttlMinutes: parseInt(process.env.SESSION_TTL_MINUTES || "30", 10),
    maxConcurrentSessions: parseInt(
      process.env.MAX_CONCURRENT_SESSIONS || "10",
      10
    ),
    scriptTimeoutMs: parseInt(process.env.SCRIPT_TIMEOUT_MS || "100", 10),
    asyncTimeoutMs: parseInt(process.env.ASYNC_TIMEOUT_MS || "30000", 10),
  },
  logging: {
    level: process.env.LOG_LEVEL ?? "info",
    file: process.env.LOG_FILE,
  },
};

export default config;
